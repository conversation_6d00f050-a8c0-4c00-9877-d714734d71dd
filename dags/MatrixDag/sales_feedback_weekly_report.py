from airflow import DAG
from airflow.models import Variable
from datetime import datetime, timedelta
from airflow.operators.python_operator import PythonOperator
from utils.email_utils import send_email
import pyodbc
import openpyxl
import pendulum
import requests

server = Variable.get("common_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
fetchCustTicketAthena_url = Variable.get("fetchCustTicketAthena_url")

default_args = {
    'owner': 'Shikha Agrawal',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2025, 5, 5, tz="Asia/Kolkata"),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'sales_feedback_weekly_report',
    default_args=default_args,
    schedule_interval="0 14 * * 0",
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"]
)

connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"


def process_sql_table(cursor, body, table_head=''):
    table_data = cursor.fetchall()
    table_columns = [desc[0] for desc in cursor.description]

    if table_data:
        if table_head:
            body += f"<p><b>{table_head}</b></p>"

        body += "<table border='1'><thead style='background-color: #f2f2f2; font-weight: bold;'><tr>"
        for column_name in table_columns:
            body += f"<th>{column_name}</th>"
        body += "</tr></thead><tbody>"

        for row in table_data:
            body += "<tr>" + "".join(f"<td style='text-align: center;'>{value}</td>" for value in row) + "</tr>"

        body += "</tbody></table><br><br>"

    return body


def process_attachment(cursor, filename):
    table_data = cursor.fetchall()
    table_columns = [desc[0] for desc in cursor.description]

    fileName = filename
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.append(table_columns)

    for row in table_data:
        worksheet.append(list(row))

    workbook.save(fileName)
    return fileName


def getContentToSend(cursor):
    body = ""
    filesToAttach = []

    if cursor.description:
        body = process_sql_table(cursor, body, "BU Wise total tickets Status:")

    if cursor.nextset() and cursor.description:
        body = process_sql_table(cursor, body, "BU Wise Open Tickets Timeline")

    if cursor.nextset() and cursor.description:
        body = process_sql_table(cursor, body, "Category-wise open tickets in each BU:")

    if cursor.nextset() and cursor.description:
        filesToAttach.append(process_attachment(cursor,"RawData.xlsx"))

    return { "Body": body, "Attachment": filesToAttach}


def getCustTicketSummaryBMS(jsonBody={}):
    try:
        response = requests.get(fetchCustTicketAthena_url, json=jsonBody)

        if response.status_code==200:
            responseJson= response.json()
            return responseJson['data']
        else:
            return None
    except Exception as e:
        print("Exception in fetching data from athena: ",e)


def getTicketSummary():
     
    connection = pyodbc.connect(connection_string_matrix)
    cursor= connection.cursor()

    try: 
        procedure_name="[CARE].[SalesFeedbackDailySummaryReport]"
        cursor.execute(f"EXEC {procedure_name}")

        subject = "Internal Escalations by Sales Advisors || Weekly Report"
        content = getContentToSend(cursor)
        recipients = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]

        if content["Body"]:
            send_email(recipients, subject, content["Body"], "Genie Report", content["Attachment"])

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        cursor.close()
        connection.close()

sales_feedback_weekly_report = PythonOperator(
    task_id='sales_feedback_weekly_report',
    python_callable=getTicketSummary,
    provide_context=True,
    dag=dag,
)

sales_feedback_weekly_report