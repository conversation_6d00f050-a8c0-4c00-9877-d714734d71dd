from airflow import DAG
from airflow.operators.python_operator import Python<PERSON><PERSON><PERSON>
from airflow.hooks.mssql_hook import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from pymongo import MongoClient
import pendulum
import pyodbc
import json
from airflow.providers.mongo.hooks.mongo import MongoHook

# Connection strings and configurations
from ConnectionConfigs.ConnectionString import ConnectionString
connection_string_matrix = ConnectionString(30, "Matrix")


# Default arguments for the DAG
default_args = {
    'owner': '<PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 1, 1, tz="Asia/Kolkata"),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'BestTimeToCallDAG',
    default_args=default_args,
    description='Get best time to call parameters for recently called customers',
    schedule_interval='*/30 * * * *',  # Run every 30 minutes
    catchup=False,
    max_active_runs=1,
)

def get_recent_called_customers():
    """Get list of recently called customer IDs"""
    customer_ids = []
    
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        # Execute the stored procedure to get recent called customers
        cursor.execute("EXEC [MTX].[GetRecentCalledCustomers]")
        rows = cursor.fetchall()
        
        # Extract customer IDs
        for row in rows:
            customer_ids.append(row.CustomerID)
            
        cursor.close()
        connection.close()
        
        print(f"Retrieved {len(customer_ids)} customer IDs")
        return customer_ids
        
    except Exception as e:
        print(f"Error getting recent called customers: {str(e)}")
        return []

def process_customer_data(customer_ids):
    """Process each customer ID to get best time to call parameters and store in MongoDB"""
    if not customer_ids:
        print("No customer IDs to process")
        return
    
    try:
        # Connect to MongoDB
        # mongo_client = MongoClient(mongo_connection_string)
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['BestTimeToCall']
        
        # Connect to SQL Server
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        processed_count = 0
        
        # All possible days of the week
        days_of_week = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        # Process each customer ID
        for customer_id in customer_ids:
            try:
                print(1)
                # Execute the stored procedure to get best time to call parameters
                cursor.execute(f"EXEC [MTX].[BestTimeToCallParams] @CustomerId = {customer_id}")
                rows = cursor.fetchall()
                print(2)
                if not rows:
                    continue
                
                # Initialize customer data with empty arrays for each day
                customer_data = {
                    "CustomerID": customer_id,
                    "UpdatedOn": datetime.now(),
                    "TimeSlots": {day: [] for day in days_of_week}
                }
                print(3)
                # Process each time slot and organize by day
                for row in rows:
                    day_name = row.day_name
                    time_slot = {
                        "HourOfDay": row.hour_of_day,
                        "AnswerRatePercent": float(row.answer_rate_pct),
                        "TotalAttempts": row.total_attempts,
                        "AnsweredCount": row.answeredCount,
                        "UnansweredCount": row.unansweredCount,
                        "AvgTalkSeconds": row.avg_talk_seconds,
                        "CallScore": row.call_score,
                        "Recommendation": row.recommendation
                    }
                    
                    # Add time slot to the appropriate day
                    customer_data["TimeSlots"][day_name].append(time_slot)
                print(4)
                print(customer_id)
                print(customer_data)
                existing_data = collection.find_one({"CustomerID": 10761368})
                print("Existing Data", existing_data)
                existing_data = collection.find_one({"CustomerID": customer_id})
                print("Existing Data", existing_data)
                
                collection.insert_one({"CustomerID": customer_id, "customer_data":customer_data})
                # Upsert to MongoDB (update if exists, insert if not)
                #collection.update_one(
                #    {"CustomerID": customer_id},
                #    {"$set": customer_data},
                #    upsert=True
                #)
                print(5)
                
                processed_count += 1
                
            except Exception as e:
                print(f"Error processing customer ID {customer_id}: {str(e)}")
                continue
        
        cursor.close()
        connection.close()
        mongo_client.close()
        
        print(f"Successfully processed {processed_count} customers")
        
    except Exception as e:
        print(f"Error in process_customer_data: {str(e)}")

def execute_best_time_to_call_workflow():
    """Main workflow function"""
    customer_ids = get_recent_called_customers()
    process_customer_data(customer_ids)

# Define the task
best_time_to_call_task = PythonOperator(
    task_id='best_time_to_call_task',
    python_callable=execute_best_time_to_call_workflow,
    dag=dag,
)

# Set task dependencies (only one task in this case)
best_time_to_call_task