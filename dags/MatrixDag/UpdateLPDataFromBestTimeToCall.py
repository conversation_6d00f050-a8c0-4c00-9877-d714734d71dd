from airflow import DAG
from airflow.operators.python_operator import Python<PERSON>perator
from airflow.models import Variable
from datetime import datetime, timedelta
import pendulum
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson.objectid import ObjectId
# Default arguments for the DAG
default_args = {
    'owner': '<PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 1, 1, tz="Asia/Kolkata"),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'UpdateLPDataFromBestTimeToCall',
    default_args=default_args,
    description='Update LPData collection with NBT data from BestTimeToCall collection',
    schedule_interval='* * * * *',  # Run every 15 minutes to process batches quickly
    catchup=False,
    max_active_runs=1,
)

def update_lpdata_from_besttimetocall():
    """Read data from BestTimeToCall collection and update LPData collection"""
    
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        
        # Collections
        best_time_collection = db['BestTimeToCall']
        lp_collection = db['LPData']
        
        # print("Starting LPData update process from BestTimeToCall collection")
        
        # Get configuration from Airflow Variables
        batch_size = int(Variable.get("NBT_LPData_Update_BatchSize", default_var="5000"))  # Process 5000 records at a time
        max_leads_per_customer = int(Variable.get("NBT_LPData_Update_MaxLeadsPerCustomer", default_var="50"))

        # Get the last processed ObjectId to continue from where we left off
        last_processed_id = Variable.get("NBT_LPData_LastProcessedObjectId", default_var=None)

        # print(f"Configuration: batch_size={batch_size}, max_leads_per_customer={max_leads_per_customer}")
        # print(f"Last processed ObjectId: {last_processed_id}")

        # Build query to get next batch of customers
        
        query = {}
        if last_processed_id:
            try:
                query = {"_id": {"$gt": ObjectId(last_processed_id)}}
            except Exception as e:
                query = {}
        else:
            print("Starting from the beginning of the collection")

        # Get batch of customers from BestTimeToCall collection with pagination
        customers_cursor = best_time_collection.find(
            query,
            {"CustomerID": 1, "TimeSlots": 1}
        ).sort("_id", 1).limit(batch_size)
        
        total_customers = 0
        processed_customers = 0
        updated_leads = 0
        skipped_customers = 0
        error_count = 0
        last_object_id = None

        # Process the batch of customers (limited to batch_size)
        batch = []

        for customer_doc in customers_cursor:
            total_customers += 1
            customer_id = customer_doc.get("CustomerID")
            time_slots = customer_doc.get("TimeSlots")
            last_object_id = str(customer_doc.get("_id"))  # Track the last processed ObjectId

            if not customer_id or not time_slots:
                skipped_customers += 1
                continue

            batch.append({
                "customer_id": customer_id,
                "time_slots": time_slots,
                "object_id": last_object_id
            })

        # Process the entire batch at once (since we're already limiting to batch_size)
        if batch:
            print(f"Processing batch of {len(batch)} customers")
            batch_results = process_customer_batch(lp_collection, batch, max_leads_per_customer)
            processed_customers += batch_results["processed"]
            updated_leads += batch_results["updated_leads"]
            error_count += batch_results["errors"]

            # Update the last processed ObjectId in Airflow Variable
            if last_object_id:
                try:
                    Variable.set("NBT_LPData_LastProcessedObjectId", last_object_id)
                except Exception as e:
                    print(f"Error updating LPData_LastProcessedObjectId variable: {e}")
        else:
            print("No customers found in this batch - may have reached end of collection")
            # Reset the variable to start from beginning next time
            # try:
            #     Variable.delete("NBT_LPData_LastProcessedObjectId")
            # except Exception as e:
            #     print(f"Error resetting LPData_LastProcessedObjectId variable: {e}")
        
        # Close MongoDB connection
        client.close()
        
        # Print summary
        print(f"\n=== LPData Update Summary ===")
        print(f"Batch size: {batch_size}")
        print(f"Customers in this batch: {total_customers}")
        print(f"Customers processed: {processed_customers}")
        print(f"Customers skipped: {skipped_customers}")
        print(f"Total leads updated: {updated_leads}")
        print(f"Errors encountered: {error_count}")
        print(f"Last processed ObjectId: {last_object_id}")

        if total_customers > 0:
            success_rate = (processed_customers / total_customers) * 100
            print(f"Success rate: {success_rate:.1f}%")

        # Provide guidance on progress
        if total_customers == batch_size:
            print(f"📊 Processed full batch of {batch_size} records. More records may be available.")
        elif total_customers < batch_size and total_customers > 0:
            print(f"📊 Processed {total_customers} records (less than batch size). Nearing end of collection.")
        elif total_customers == 0:
            print(f"📊 No more records to process. Collection processing complete.")
        
    except Exception as e:
        print(f"Error in update_lpdata_from_besttimetocall: {str(e)}")
        raise

def process_customer_batch(lp_collection, customer_batch, max_leads_per_customer):
    """Process a batch of customers and update their LPData records"""
    
    processed = 0
    updated_leads = 0
    errors = 0
    
    for customer_data in customer_batch:
        try:
            customer_id = customer_data["customer_id"]
            time_slots = customer_data["time_slots"]
            
            # Find active leads for this customer
            leadids = [
                doc["_id"] for doc in lp_collection.find(
                    {"CustID": customer_id, "IsActive": True}, 
                    projection={"_id": 1},
                    limit=max_leads_per_customer, 
                    sort=[("UpdatedOn", -1)]
                )
            ]
            
            if leadids:
                # Update LPData records with NBT (Next Best Time) data
                result = lp_collection.update_many(
                    {"_id": {"$in": leadids}},
                    {
                        "$set": {
                            "NBT": time_slots
                        }
                    }
                )
                
                if result.modified_count > 0:
                    updated_leads += result.modified_count
                    if result.modified_count > 10:  # Only log for significant updates
                        print(f"Updated NBT for {result.modified_count} leads for customer {customer_id}")
                
                processed += 1
            else:
                # No active leads found for this customer
                pass
                
        except Exception as e:
            print(f"Error processing customer {customer_data.get('customer_id', 'unknown')}: {str(e)}")
            errors += 1
            continue
    
    return {
        "processed": processed,
        "updated_leads": updated_leads,
        "errors": errors
    }

def check_collections_status():
    """Check the status of both collections for monitoring"""

    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB

        # Collections
        best_time_collection = db['BestTimeToCall']
        lp_collection = db['LPData']

        # Get collection statistics
        best_time_count = best_time_collection.count_documents({})
        lp_data_count = lp_collection.count_documents({"IsActive": True})
        lp_data_with_nbt = lp_collection.count_documents({"IsActive": True, "NBT": {"$exists": True}})

        # Get progress information
        last_processed_id = Variable.get("NBT_LPData_LastProcessedObjectId", default_var=None)

        print(f"\n=== Collection Status ===")
        print(f"BestTimeToCall records: {best_time_count:,}")
        print(f"Active LPData records: {lp_data_count:,}")
        print(f"LPData records with NBT: {lp_data_with_nbt:,}")

        if lp_data_count > 0:
            nbt_coverage = (lp_data_with_nbt / lp_data_count) * 100
            print(f"NBT coverage: {nbt_coverage:.1f}%")

        # Calculate processing progress
        if last_processed_id and best_time_count > 0:
            try:
                # Use string comparison for ObjectId - MongoDB handles this automatically
                processed_count = best_time_collection.count_documents({"_id": {"$lte": last_processed_id}})
                progress_percentage = (processed_count / best_time_count) * 100
                remaining_records = best_time_count - processed_count

                print(f"\n=== Processing Progress ===")
                print(f"Records processed: {processed_count:,} / {best_time_count:,}")
                print(f"Progress: {progress_percentage:.1f}%")
                print(f"Remaining records: {remaining_records:,}")
                print(f"Last processed ObjectId: {last_processed_id}")

                # Estimate completion time (assuming 5000 records every 15 minutes)
                batch_size = int(Variable.get("NBT_LPData_Update_BatchSize", default_var="5000"))
                remaining_batches = (remaining_records + batch_size - 1) // batch_size  # Ceiling division
                estimated_minutes = remaining_batches * 15
                estimated_hours = estimated_minutes / 60

                if estimated_hours < 1:
                    print(f"Estimated completion: ~{estimated_minutes} minutes")
                else:
                    print(f"Estimated completion: ~{estimated_hours:.1f} hours")

            except Exception as progress_error:
                print(f"Error calculating progress: {str(progress_error)}")
        else:
            print(f"\n=== Processing Progress ===")
            print("Processing not started yet or no tracking ObjectId found")

        client.close()

    except Exception as e:
        print(f"Error checking collections status: {str(e)}")

def execute_lpdata_update_workflow():
    """Main workflow function"""
    
    print(f"=== Starting LPData Update Workflow at {datetime.now()} ===")
    
    # Check collections status before processing
    # check_collections_status()

    # Perform the update
    update_lpdata_from_besttimetocall()

    # Check collections status after processing
    # print(f"\n=== Post-Update Status ===")
    # check_collections_status()
    
    print(f"=== Completed LPData Update Workflow at {datetime.now()} ===")

# Define the task
update_lpdata_task = PythonOperator(
    task_id='update_lpdata_from_besttimetocall',
    python_callable=execute_lpdata_update_workflow,
    dag=dag,
)

# Set task dependencies (only one task in this case)
update_lpdata_task
