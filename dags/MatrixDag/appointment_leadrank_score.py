from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc

live_server = Variable.get("primary_matrixdb")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")

default_args = {
    'owner': '<PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': datetime(2025, 6, 12),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 0
}

dag = DAG(
    'appointment_leadrank_score',
    default_args=default_args,
    schedule_interval=timedelta(minutes=1),
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"]
)

live_connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={live_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

def setAppointmentLeadRankScore():
     
    connection = pyodbc.connect(live_connection_string_matrix)
    cursor= connection.cursor()

    try: 
        procedure_name="[MTX].[SetAppointmentLeadRankScore]"
        cursor.execute(f"EXEC {procedure_name}")
        connection.commit()

        cursor.close()
        connection.close()

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("Completed")

appointment_leadrank_score = PythonOperator(
    task_id='appointment_leadrank_score',
    python_callable=setAppointmentLeadRankScore,
    provide_context=True,
    dag=dag,
)

appointment_leadrank_score