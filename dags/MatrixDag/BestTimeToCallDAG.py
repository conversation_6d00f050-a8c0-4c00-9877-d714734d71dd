from airflow import DAG
from airflow.operators.python_operator import Python<PERSON><PERSON>ator
from airflow.hooks.mssql_hook import <PERSON><PERSON><PERSON>l<PERSON><PERSON>
from airflow.models import Variable
from datetime import datetime, timedelta
import pendulum
import pyodbc
import json
from airflow.providers.mongo.hooks.mongo import MongoHook

# Connection strings and configurations
from ConnectionConfigs.ConnectionString import ConnectionString
connection_string_matrix = ConnectionString(30, "Matrix_Primary")


# Default arguments for the DAG
default_args = {
    'owner': '<PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 1, 1, tz="Asia/Kolkata"),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'BestTimeToCallDAG',
    default_args=default_args,
    description='Get best time to call parameters for recently called customers',
    schedule_interval='45 21 * * *',  
    catchup=False,
    max_active_runs=1,
)

def get_recent_called_customers():
    """Get list of recently called customer IDs"""
    customer_ids = []
    
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        # Execute the stored procedure to get recent called customers
        cursor.execute("EXEC [MTX].[GetRecentCustomers]")
        rows = cursor.fetchall()
        
        # Extract customer IDs
        for row in rows:
            customer_ids.append(row.CustomerID)
            
        cursor.close()
        connection.close()
        
        print(f"Retrieved {len(customer_ids)} customer IDs")
        return customer_ids
        
    except Exception as e:
        print(f"Error getting recent called customers: {str(e)}")
        return []

def process_customer_data(customer_ids, force_refresh=False):
    """Process each customer ID to get best time to call parameters and store in MongoDB

    Args:
        customer_ids: List of customer IDs to process
        force_refresh: If True, skip existing data check and always query SQL database
    """
    if not customer_ids:
        print("No customer IDs to process")
        return

    if force_refresh:
        print("Force refresh enabled - will query SQL database for all customers")
    
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['BestTimeToCall']
        lp_collection = db['LPData']
        
        # Connect to SQL Server
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        processed_count = 0
        existing_data_count = 0
        new_data_count = 0

        # All possible days of the week
        days_of_week = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        # Process each customer ID
        for customer_id in customer_ids:
            try:
                # First, check if data already exists in MongoDB BestTimeToCall collection (unless force refresh)
                existing_data = None if force_refresh else collection.find_one({"CustomerID": customer_id})

                if existing_data and not force_refresh:
                    processed_count += 1
                    existing_data_count += 1    
                    # Still update LPData if this customer qualifies (every 10th customer)
                
                    try:
                        leadids = [doc["_id"] for doc in lp_collection.find({"CustID": customer_id, "IsActive": True}, projection={"_id": 1}, limit=50, sort=[("UpdatedOn", -1)])]

                        if leadids and "TimeSlots" in existing_data:
                            result = lp_collection.update_many(
                                {"_id": {"$in": leadids}},
                                {"$set": {"NBT": existing_data["TimeSlots"]}}
                            )
                            # if result.modified_count > 0:
                            #     print(f"Updated NBT for {result.modified_count} records in LPData with existing data for CustID {customer_id}")
                    except Exception as e:
                        print(f"Error updating LPData with existing data for customer ID {customer_id}: {str(e)}")

                    continue  # Skip SQL query and processing for this customer

                # If no existing data, proceed with SQL query
                cursor.execute(f"EXEC [MTX].[BestTimeToCallParams_Optimized] @CustomerId = {customer_id}")

                # Check if there are results to fetch
                try:
                    rows = cursor.fetchall()
                    has_results = True
                except pyodbc.ProgrammingError as pe:
                    if "No results. Previous SQL was not a query." in str(pe):
                        print(f"No results returned for customer ID {customer_id}")
                        has_results = False
                    else:
                        # Re-raise if it's a different error
                        raise

                if not has_results or not rows:
                    # print(f"Skipping customer ID {customer_id} - no valid data")
                    continue
                
                # Initialize customer data with empty arrays for each day
                customer_data = {
                    "CustomerID": customer_id,
                    "UpdatedOn": datetime.now(),
                    "TimeSlots": {day: [] for day in days_of_week}
                }
              
                # Process each time slot and organize by day
                for row in rows:
                    day_name = row.day_name
                    time_slot = {
                        "HourOfDay": row.hour_of_day,
                        # "AnswerRatePercent": float(row.answer_rate_pct),
                        # "TotalAttempts": row.total_attempts,
                        # "AnsweredCount": row.answeredCount,
                        # "UnansweredCount": row.unansweredCount,
                        # "AvgTalkSeconds": row.avg_talk_seconds,
                        "CallScore": row.call_score,
                        "Recommendation": row.recommendation
                    }
                    
                    # Add time slot to the appropriate day
                    customer_data["TimeSlots"][day_name].append(time_slot)
                
                # Upsert to MongoDB (update if exists, insert if not)
                collection.update_one(
                    {"CustomerID": customer_id},
                    {"$set": customer_data},
                    upsert=True
                )
                
                # 1% of customers, Update Records in LP data having CustID:customer_id and IsActive:true, set NBT : customer_data["TimeSlots"]
                try:
                    leadids = [doc["_id"] for doc in lp_collection.find({"CustID": customer_id, "IsActive": True}, projection={"_id": 1},limit=50, sort=[("UpdatedOn", -1)])]

                    result = lp_collection.update_many(
                        {"_id": {"$in": leadids}},
                        {"$set": {"NBT": customer_data["TimeSlots"]}}
                    )
                    # if result.modified_count > 10: #less logs
                    #     print(f"Updated NBT for {result.modified_count} records in LPData with CustID {customer_id}")
                    # else:
                        # print(f"No active records found in LPData for CustID {customer_id}")
                except Exception as e:
                    print(f"Error updating LPData for customer ID {customer_id}: {str(e)}")
                
                processed_count += 1
                new_data_count += 1
                if(processed_count % 100 == 0):
                    connection.commit()
                    cursor = connection.cursor()
            except Exception as e:
                print(f"Error processing customer ID {customer_id}: {str(e)}")
                connection = pyodbc.connect(connection_string_matrix) #reconnect if failed
                cursor = connection.cursor()
                continue
        
        cursor.close()
        connection.commit()
        connection.close()
        client.close()

        print(f"Successfully processed {processed_count} customers")
        print(f"  - Used existing data: {existing_data_count} customers")
        print(f"  - Processed new data: {new_data_count} customers")
        # if processed_count > 0:
        #     print(f"  - Performance improvement: {(existing_data_count/processed_count*100):.1f}% of customers used cached data")
        
    except Exception as e:
        print(f"Error in process_customer_data: {str(e)}")

def execute_best_time_to_call_workflow():
    """Main workflow function"""
    # Check if force refresh is enabled via Airflow Variable
    force_refresh = Variable.get("BestTimeToCall_ForceRefresh", default_var="false").lower() == "true"

    if force_refresh:
        print("Force refresh enabled via BestTimeToCall_ForceRefresh variable")

    customer_ids = get_recent_called_customers()
    process_customer_data(customer_ids, force_refresh=force_refresh)

# Define the task
best_time_to_call_task = PythonOperator(
    task_id='best_time_to_call_task',
    python_callable=execute_best_time_to_call_workflow,
    dag=dag,
)

# Set task dependencies (only one task in this case)
best_time_to_call_task