from airflow import DAG
from airflow.operators.python_operator import Python<PERSON><PERSON><PERSON>
from airflow.hooks.mssql_hook import <PERSON><PERSON><PERSON>l<PERSON><PERSON>
from airflow.models import Variable

from datetime import datetime, timedelta
import pendulum
import pyodbc
import json
from airflow.providers.mongo.hooks.mongo import MongoHook

# Connection strings and configurations
from ConnectionConfigs.ConnectionString import ConnectionString
connection_string_matrix = ConnectionString(30, "Matrix_Primary")

# Default arguments for the DAG
default_args = {
    'owner': '<PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 1, 1, tz="Asia/Kolkata"),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'BestTimeToCallDAG2',
    default_args=default_args,
    description='Get best time to call parameters for customers',
    schedule_interval='* * * * *',  # Run every hour from 10 PM to 9 AM
    catchup=False,
    max_active_runs=1,
)

def get_customers():
    """Get list of recently called customer IDs"""
    customer_ids = []
    
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        BestTimeToCallDateTime = Variable.get("BestTimeToCallDateTime", default_var=None)

        # Calculate date range based on BestTimeToCallDateTime
        from datetime import datetime, timedelta

        if BestTimeToCallDateTime:
            # Parse the datetime from the variable
            try:
                start_date = datetime.strptime(BestTimeToCallDateTime, "%Y-%m-%d %H:%M:%S")
                print(f"Using BestTimeToCallDateTime from variable: {start_date}")
            except ValueError:
                # Try alternative format if the first one fails
                try:
                    start_date = datetime.strptime(BestTimeToCallDateTime, "%Y-%m-%d")
                except ValueError:
                    start_date = datetime.now() - timedelta(hours=24)
        else:
            # Default to last 24 hours if variable is not set
            start_date = datetime.now() - timedelta(hours=24)
            print(f"BestTimeToCallDateTime variable not set, using default: {start_date}")

        # Set end date to current time
        end_date = start_date + timedelta(hours=24)

        print(f"Querying customers created between {start_date} and {end_date}")

        # Execute the query with the calculated date range
        cursor.execute("""
            SELECT DISTINCT LD.CustomerID
            FROM Matrix.CRM.Leaddetails ld
            LEFT JOIN PBCROMA.CRM.InvalidMobileNumbers invm ON INVM.MobileNo = LD.MobileNo
            LEFT JOIN PBCROMA.MTX.CustomerCallStats CS ON CS.CustomerID = ld.CustomerID
            WHERE ld.createdon > ? AND ld.createdon < ? 
                       AND INVM.id IS NULL 
                       AND ld.parentid IS NULL 
                       AND CS.CustomerID IS NULL
                       AND ld.ProductId in (2,7,115,117)
        """, start_date, end_date)
        rows = cursor.fetchall()
        
        # Extract customer IDs
        for row in rows:
            customer_ids.append(row.CustomerID)
            
        cursor.close()
        connection.close()
        
        print(f"Retrieved {len(customer_ids)} customer IDs from {start_date} to {end_date}")

        # Log some sample customer IDs for debugging
        if customer_ids:
            sample_size = min(5, len(customer_ids))
            print(f"Sample customer IDs: {customer_ids[:sample_size]}")

        return customer_ids

    except Exception as e:
        print(f"Error getting recent called customers: {str(e)}")
        print(f"BestTimeToCallDateTime variable value was: {BestTimeToCallDateTime}")
        return []

def process_customer_data(customer_ids):
    """Process each customer ID to get best time to call parameters and store in MongoDB"""
    if not customer_ids:
        print("No customer IDs to process")
        return
    
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['BestTimeToCall']
        lp_collection = db['LPData']
        
        # Connect to SQL Server
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        processed_count = 0
        
        # All possible days of the week
        days_of_week = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        # Process each customer ID
        for customer_id in customer_ids:
            try:
                # Execute the stored procedure to get best time to call parameters
                existing_data = collection.find_one({"CustomerID": customer_id})

                if existing_data :
                    continue
                

                cursor.execute(f"EXEC [MTX].[BestTimeToCallParams_Optimized] @CustomerId = {customer_id}")
                   
                # Check if there are results to fetch
                try:
                    rows = cursor.fetchall()
                    has_results = True
                except pyodbc.ProgrammingError as pe:
                    if "No results. Previous SQL was not a query." in str(pe):
                        print(f"No results returned for customer ID {customer_id}")
                        has_results = False
                    else:
                        # Re-raise if it's a different error
                        raise
                
                if not has_results or not rows:
                    # print(f"Skipping customer ID {customer_id} - no valid data")
                    continue
                
                # Initialize customer data with empty arrays for each day
                customer_data = {
                    "CustomerID": customer_id,
                    "UpdatedOn": datetime.now(),
                    "TimeSlots": {day: [] for day in days_of_week}
                }
              
                # Process each time slot and organize by day
                for row in rows:
                    day_name = row.day_name
                    time_slot = {
                        "HourOfDay": row.hour_of_day,
                        # "AnswerRatePercent": float(row.answer_rate_pct),
                        # "TotalAttempts": row.total_attempts,
                        # "AnsweredCount": row.answeredCount,
                        # "UnansweredCount": row.unansweredCount,
                        # "AvgTalkSeconds": row.avg_talk_seconds,
                        "CallScore": row.call_score,
                        "Recommendation": row.recommendation
                    }
                    
                    # Add time slot to the appropriate day
                    customer_data["TimeSlots"][day_name].append(time_slot)
                
                # Upsert to MongoDB (update if exists, insert if not)
                collection.update_one(
                    {"CustomerID": customer_id},
                    {"$set": customer_data},
                    upsert=True
                )
                
                # 1% of customers, Update Records in LP data having CustID:customer_id and IsActive:true, set NBT : customer_data["TimeSlots"]
                # if customer_id % 2 == 0:
                try:
                    leadids = [doc["_id"] for doc in lp_collection.find({"CustID": customer_id, "IsActive": True}, projection={"_id": 1},limit=50, sort=[("UpdatedOn", -1)])]

                    result = lp_collection.update_many(
                        {"_id": {"$in": leadids}},
                        {"$set": {"NBT": customer_data["TimeSlots"]}}
                    )
                    if result.modified_count > 10: #less logs
                        print(f"Updated NBT for {result.modified_count} records in LPData with CustID {customer_id}")
                    # else:
                        # print(f"No active records found in LPData for CustID {customer_id}")
                except Exception as e:
                    print(f"Error updating LPData for customer ID {customer_id}: {str(e)}")
                
                # connection.commit()
                processed_count += 1
                if(processed_count % 100 == 0):
                    connection.commit()
                    cursor = connection.cursor()
            except Exception as e:
                print(f"Error processing customer ID {customer_id}: {str(e)}")
                connection = pyodbc.connect(connection_string_matrix) #reconnect if failed
                cursor = connection.cursor()
                continue
        cursor.close()
        connection.commit()
        connection.close()
        client.close()
        
        print(f"Successfully processed {processed_count} customers")
        
    except Exception as e:
        print(f"Error in process_customer_data: {str(e)}")

def execute_best_time_to_call_workflow():
    """Main workflow function"""
    from datetime import datetime

    print(f"=== Starting BestTimeToCall workflow at {datetime.now()} ===")
    current_var_value = None
    # Log the current BestTimeToCallDateTime variable value
    try:
        current_var_value = Variable.get("BestTimeToCallDateTime", default_var=None)
    except Exception as e:
        print(f"Error reading BestTimeToCallDateTime variable: {e}")

    # Get customers based on the BestTimeToCallDateTime variable
    customer_ids = get_customers()

    if not customer_ids:
        print("No customers to process. Workflow completed.")
        return

    # Process the customers
    process_customer_data(customer_ids)

    # Update the BestTimeToCallDateTime variable to current time for next run
    try:
        new_datetime = datetime.strptime(current_var_value, "%Y-%m-%d %H:%M:%S")- timedelta(hours=24)
        new_datetime = new_datetime.strftime("%Y-%m-%d %H:%M:%S")
        Variable.set("BestTimeToCallDateTime", new_datetime)
        print(f"Updated BestTimeToCallDateTime variable to: {new_datetime}")
    except Exception as e:
        print(f"Error updating BestTimeToCallDateTime variable: {e}")

    print(f"=== Completed BestTimeToCall workflow at {datetime.now()} ===")

# Define the task
best_time_to_call_task = PythonOperator(
    task_id='best_time_to_call_task',
    python_callable=execute_best_time_to_call_workflow,
    dag=dag,
)

# Set task dependencies (only one task in this case)
best_time_to_call_task