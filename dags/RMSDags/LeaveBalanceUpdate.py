import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum
import requests
import json
from datetime import datetime
from airflow.providers.mongo.hooks.mongo import MongoHook
from datetime import timedelta
import hashlib
# def process_batch(batch):
import requests
from requests.auth import HTTPBasicAuth
from datetime import datetime

secret_key = Variable.get("rms_RamcoSecKey")
access_key = Variable.get("rms_RamcoAccessKey")


default_args = {
    'owner': 'RMS',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 10, 26, tz="Asia/Kolkata"),
    'retries': 0,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
}

dag = DAG(
    'DarwinLeaveBalance',
    default_args=default_args,
    schedule_interval=timedelta(days=7),
    catchup=False,
)

def SaveLogs(Method, Application, TrackingID, RequestText, response, exception_detail=None):
    try:
        # Prepare the data to be logged
        data = {
            "Application": Application,
            "Method": Method,
            "TrackingID": TrackingID,
            "RequestText": RequestText,
            "ResponseText": response,
            "Requesttime": datetime.now(),
            "Responsetime": datetime.now(),
            "Exception": str(exception_detail) if exception_detail else None,
            "CreatedOn": datetime.now(),
            "CreatedBy": "Automation"
        }
        print("---Step1 --------",data)
        # Connect to MongoDB and log the data
        # hook = MongoHook(mongo_conn_id='logger')
        # hook = MongoHook(mongo_conn_id='mongo_default')
        hook = MongoHook(mochngo_conn_id='logger', conn_id='logger')
        print("---Step2 --------")
        client = hook.get_conn()
        print("---Step3 --------")
        db = client.logger
        print("Connection Successful:", db.list_collection_names())
        collection = db.Log_Collection
        print("MongoDB Connection Successful:", client.server_info())

        collection.insert_one(data)
    except requests.exceptions.RequestException as e:
        print("Request failed while logging:", e)
    except Exception as general_exception:
        print("Exception in SaveLogs:", general_exception)



def save_to_mongo(response_data):
    try:

        hook = MongoHook(mongo_conn_id='matrix_dashboard', conn_id='matrix_dashboard')
        client = hook.get_conn()
        db = client.MatrixDashboard
        collection = db.LeaveCount


        response_json = json.loads(response_data)

        if response_json['status'] == 1:
            # Extract the 'data' array from the response
            leaves_data = response_json.get('data', [])
            
            if leaves_data:
                # Insert data into MongoDB collection
                collection.insert_many(leaves_data)
                print("Data successfully saved to MongoDB.")
            else:
                print("No data to save.")
        else:
            print(f"Error in response: {response_json.get('message', 'Unknown error')}")

    except Exception as e:
        print(f"An error occurred: {e}")



def process_batch():
    api_url = 'https://pbhr.stage.darwinbox.io/leavesactionapi/leavebalance'
    response = None  # Initialize the variable

    try:
        # Payload definition
        print('------Step 1-------')
        payload = {
            "api_key": "b6ae0c8527c946aad736563d325b9fddaa83b01a1ba4abfcfa4bba32d4fbf425de463f9deab97d53c4f7a620e32e565c43ecd578c1c92871ddf1654d7d01c1fb",
            "employee_nos": ["PW00722","PW00721"],
             "leave_names": [
                "Earned Leave - Haryana",
                "Earned Leave - Maharashtra",
                "Earned Leave - Karnataka",
                "Earned Leave - Telangana",
                "Earned Leave - Punjab",
                "Earned Leave - Uttar Pradesh",
                "Earned Leave - Delhi",
                "Earned Leave - Rajasthan",
                "Earned Leave - Tamil Nadu",
                "Earned Leave - Gujarat",
                "Earned Leave - Madhya Pradesh",
                "Earned Leave - Bihar",
                "Earned Leave - West Bengal",
                "Earned Leave - Odisha_Prod",
                "Earned Leave - Jharkhand",
                "Earned Leave - Andhra Pradesh",
                "Earned Leave - Kerala",
                "Earned Leave - Chhattisgarh",
                "Earned Leave - Uttarakhand_Prod",
                "Casual Leave - Chhattisgarh",
                "Sick Leave - Maharashtra",
                "Sick Leave - Karnataka",
                "Sick Leave - Telangana",
                "Sick Leave - Punjab",
                "Sick Leave - Uttar Pradesh",
                "Sick Leave - Tamil Nadu",
                "Sick Leave - Gujarat",
                "Sick Leave - Madhya Pradesh",
                "Sick Leave - Bihar",
                "Sick Leave - West Bengal",
                "Sick Leave - Odisha_Prod",
                "Sick Leave - Jharkhand",
                "Sick Leave - Andhra Pradesh",
                "Sick Leave - Kerala",
                "Casual Leave - Haryana",
                "Casual leave - Maharashtra",
                "Casual Leave - Karnataka",
                "Casual Leave - Telangana",
                "Casual leave - Punjab",
                "Casual Leave - Uttar Pradesh",
                "Casual Leave - Delhi",
                "Casual Leave - Tamil Nadu",
                "Casual Leave - Gujarat",
                "Casual Leave - Madhya Pradesh",
                "Casual Leave - Bihar",
                "Casual Leave - West Bengal",
                "Casual Leave - Jharkhand",
                "Casual Leave - Andhra Pradesh",
                "Casual Leave - Kerala",
                "Casual Leave - Chhatishgarh",
                "Casual Leave - Uttarakhand_Prod",
                "Maternity Leave",
                "Paternity Leave",
                "Maternity Leave Extension",
                "Leave Without Pay",
                "Forgot to Punch",
                "Official Duty Leave",
                "Weekly Off Leave",
                "Sick Leave - Haryana",
                "Work from Home",
                "Gardening Leave",
                "ESIC Payable",
                "Compensatory Off Availment",
                "Earned Leave - Haryana(V)",
                "Casual Leave - Haryana(V)",
                "Sick Leave - Haryana(V)",
                "Earned Leave - Maharashtra(V)",
                "Sick Leave - Maharashtra(V)",
                "Casual leave - Maharashtra(V)"
            ],
            "show_hidden_leaves":"1"
        }
        headers = {
            'Content-Type': 'application/json',
        }
        username = "pb_leave"
        password = "PB@vista@123"

        Logs = f"api_url: {api_url}\n - ------payload: {payload}\n ------- headers: {headers}\n"
        # Make API call
        response = requests.post(api_url, headers=headers, json=payload, auth=HTTPBasicAuth(username, password), timeout=500)
        # Log response
        SaveLogs(Method="GetLeavesCount", Application="Airflow", TrackingID="0", RequestText=Logs, response=response.text, exception_detail="")
        
        if response.status_code == 200:
            save_to_mongo(response.text)
            print("Response received:", response.text)
            leaves = json.loads(response.text)
            if leaves['status'] == 1:
                for leave in leaves['data']:
                    print(f"Processing leave record: {leave}")        
        else:
            print("Request failed with status code:", response.status_code)

    except Exception as e:
        exception_detail = str(e)
        error_response = response.text if response else "No response received"
        SaveLogs(Method="GetLeavesCount", Application="Airflow", TrackingID="0", RequestText=Logs, response=error_response, exception_detail=exception_detail)
        print("Exception occurred:", e)

def getUsersDetails():
    connection_string = ConnectionString(150, "RMS")
    row = None  # Initialize row for returning in case of an exception
    try:
        proc_name = '[RMS].[GetUserDetails]'
        # Establish connection
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        # Execute stored procedure
        cursor.execute(f"EXEC {proc_name}")
        row = cursor.fetchall()
    except pyodbc.OperationalError as e:
        print(f"Operational Error while fetching user details: {e}")
    except pyodbc.Error as e:
        print(f"Database Error while fetching user details: {e}")
    except Exception as e:
        print(f"Unexpected Error while fetching user details: {e}")
    finally:
        # Ensure cursor and connection are closed
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
    return row


def fnLeaveBalanceUpdate():
     try:
       process_batch()
      # usersData = getUsersDetails()

      # if len(usersData)>0:
      #  user_ids = [user[1] for user in usersData]  # Mock user data
      # #  print("--user_ids--",user_ids)
      #  batch_size = 10
      #  batch_process_users(user_ids, batch_size)
     except Exception as e:
        print("Exception:", e)


def batch_process_users(user_ids, batch_size):

    for i in range(0, len(user_ids), batch_size):
        batch = user_ids[i:i + batch_size]
        print("--batch--",batch)
        process_batch(batch)

execute_LeaveBalanceUpdate_task = PythonOperator(
    task_id='execute_LeaveBalanceUpdate_task',
    python_callable=fnLeaveBalanceUpdate,
    dag=dag,
)

execute_LeaveBalanceUpdate_task