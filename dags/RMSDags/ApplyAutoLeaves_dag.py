import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
from datetime import datetime
import pyodbc
import pendulum


default_args = {
    'owner': 'VISTA',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 2, 29, tz="Asia/Kolkata"),
    'retries': 1,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'ApplyAutoLeaves_dag',
    default_args=default_args,
    schedule_interval='10 16 * * 4',
    catchup=False,
)

connection_string = ConnectionString(150,"RMS")

def InsertForceLeaves(slotsAvailable,UserId, connection,cursor):
     try:
        AvailableDates=[]
        for slot in slotsAvailable:
               formatted_date = slot.MonthlyDate.strftime('%Y-%m-%d')
               AvailableDates.append(formatted_date)
        print("AvailableDate " , AvailableDates)
        print("slotsAvaILABLE: ",slotsAvailable)
        AllAvailableDates=",".join(AvailableDates)
        if len(slotsAvailable)>0:
            print("slots ",slotsAvailable[0])
            leaveDate = slotsAvailable[0].MonthlyDate.strftime('%Y-%m-%d')
            insertparams = (UserId, 1, leaveDate, slotsAvailable[0].RequestDates, AllAvailableDates)
            proc_name="[RMS].[InsertLeaveRequest]"
            print(f"EXEC {proc_name} @UserId=?, @LeaveType=? , @LeaveDate=?, @RequestLeave=?, @AvailableDates=?",insertparams)
            cursor.execute(f"EXEC {proc_name} @UserId=?, @LeaveType=? , @LeaveDate=?, @RequestLeave=?, @AvailableDates=?",insertparams)
            connection.commit()
            #row= cursor.fetchone()
            #print(f"EXEC {proc_name} @UserId=?, @LeaveType=?, @Leavedate=?, @RequestLeave=?, @AvailableDates=?", insertparams)
            #if(row):
                  #print("row is ",row)
     except pyodbc.OperationalError as e:
        print("OperationalERROR:",e)

     except pyodbc.Error as e:
        print("Error:", e)

     except Exception as e:
        print("Exception:", e)

def GetSlotAvailability(agent):
     try:
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        pendingLeaves= agent.PendingLeaves
        i=0
        while i < pendingLeaves:
            proc_name="[RMS].[GetLeaveTakenAndSlotAvailibility_v2]"
            insertparams = (agent.UserId, 1)
            cursor.execute(f"EXEC {proc_name} @UserId=?, @ForAuto=?", insertparams)
            print(f"EXEC {proc_name} @UserId=?, @IsPresent=?", insertparams)
            slotsAvailable= cursor.fetchall()
            InsertForceLeaves(slotsAvailable,agent.UserId, connection,cursor)
            i=i+1
        print("Slot available")

     except pyodbc.OperationalError as e:
        print("OperationalERROR:",e)

     except pyodbc.Error as e:
        print("Error:", e)

     except Exception as e:
        print("Exception:", e)

     finally:
        cursor.close()
        connection.close()
        print("OK")


def fetchPendingLeaves():

     try:
        query= 'Select TOP(1) Id AS RosterId from Rms.RosterMaster order by CreatedOn DESC'
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        cursor.execute(query)
        row= cursor.fetchone()
        if(row):
             RosterId= row.RosterId
             print(f"rOSTERid IS ", RosterId)
             query3= 'Select EventDate from RMS.RosterEvents where EventId=4 and RosterId=?'
             cursor.execute(query3, RosterId)
             row2= cursor.fetchone()
             if(row2):
               EventDate= row2.EventDate
               Today= datetime.now() + timedelta(hours=5, minutes=30)
               #print(EventDate.date(), Today.date(),EventDate.time(),Today.time())
               if EventDate.date()== Today.date() and EventDate.time()<Today.time():
                  update_query= 'Update RMS.RosterMaster set FreezedForAgent = ? where Id=?'
                  cursor.execute(update_query, 0, RosterId)
                  connection.commit()
                  print('Update freeze for agent')
                  updateEvent_query = 'Update RMS.RosterEvents set EndDate = EndDate + 1 where EventId in (2,3) and RosterId=?'
                  cursor.execute(updateEvent_query, RosterId)
                  connection.commit()
                  print(updateEvent_query, RosterId)
                  print('Update Roster Events')
                  procedure_name="[RMS].[PendingLeaves]"
                  insertparams=RosterId
                  cursor.execute(f"EXEC {procedure_name} @RosterId=?", insertparams)
                  print(f"EXEC {procedure_name} @RosterId=?", insertparams)
                  pendingLeaves= cursor.fetchall()
                  cursor.close()
                  connection.close()
                  for agent in pendingLeaves:
                        print("agent printing", agent)
                        GetSlotAvailability(agent)
                  connection= pyodbc.connect(connection_string)
                  cursor= connection.cursor()
                  cursor.execute(update_query, 1, RosterId)
                  connection.commit()
                  print('Update freeze for agent back to 1')
                  updateEvent_query2= 'Update RMS.RosterEvents set EndDate = EndDate -1 where EventId in (2,3) and RosterId=?'
                  cursor.execute(updateEvent_query2, RosterId)
                  connection.commit()
                  print(updateEvent_query2, RosterId)
                  print('Update Roster Events2')
               else:
                  print('No need to apply auto leaves')
             else:
               print("Unable to fetch eventDate for RosterID ",RosterId)
        else:
             print("no return")

     except pyodbc.OperationalError as e:
        print("OperationalERROR:",e)
        
     except pyodbc.Error as e:
        print("Error:", e)
   
     except Exception as e:
        print("Exception:", e)
        #connection= pyodbc.connect(connection_string)
        #cursor= connection.cursor()
        #cursor.execute(update_query, 1, RosterId)
        #print('Update freeze for agent back to 1')
        #updateEvent_query2= 'Update RMS.RosterEvents set EndDate = EndDate -1 where EventId in (2,3) and RosterId=?'
        #cursor.execute(updateEvent_query2, RosterId)
        #print('Update Roster Events2')

     finally:
        cursor.close()
        connection.close()
        print("OK")

ApplyAutoLeaves_task = PythonOperator(
    task_id='ApplyAutoLeaves_task',
    python_callable=fetchPendingLeaves,
    dag=dag,
)

ApplyAutoLeaves_task
