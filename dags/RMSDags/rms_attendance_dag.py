from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc
import pendulum

matrix_server=Variable.get("common_db_server")
rms_server= Variable.get("rms_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
rms_db= Variable.get("dbRMS")
rms_user=Variable.get("rms_username")
rms_pwd= Variable.get("rms_password")

default_args = {
    'owner': 'VISTA',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 9, 4, tz="Asia/Kolkata"),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'execute_attendance_dag_rms',
    default_args=default_args,
    schedule_interval='0 11-22 * * *',
    catchup=False,
)

connection_string_matrix=f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={matrix_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"
connection_string_rms= f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={rms_server};DATABASE={rms_db};UID={rms_user};PWD=*********"

def insertAttendanceData(attendance_list):
     
     connection= pyodbc.connect(connection_string_rms)
     cursor= connection.cursor()

     try:
         procedure_name="[RMS].[UserAttendanceData]"
         for user in attendance_list:
                   if 'UserId' in user:  # Check if 'UserId' key exists in the dictionary
                           params = {'UserId': user['UserId'], 'IsPresent': user['IsPresent']}
                           insertparams = (params['UserId'], params['IsPresent'])
                           cursor.execute(f"EXEC {procedure_name} @UserId=?, @IsPresent=?", insertparams)
                           print(f"EXEC {procedure_name} @UserId=?, @IsPresent=?", insertparams)
                           connection.commit()
                   else:
                           print(f"User object has no UserId attribute: {user}")

     except pyodbc.Error as e:
        print("Error:", e)

     finally:
          cursor.close()
          connection.close() 

def getAttendanceFrom_matrix(rows):
     
     connection = pyodbc.connect(connection_string_matrix)
     cursor= connection.cursor()

     try:
         
         procedure_name="[MTX].[IsAgentPresent_RMS]"
         attendance_list=[]
         for row in rows:
             
             insertparams= row.UserId
             
             cursor.execute(f"EXEC {procedure_name} {insertparams}")
             attendance= cursor.fetchone()
             attendance_list.append({'UserId':row.UserId,'IsPresent':attendance.isAgentPresent})

         cursor.close()
         connection.close()
         insertAttendanceData(attendance_list)

     except pyodbc.Error as e:
        print("Error:", e)

     finally:
             print("finally")

def fetchusersfrom_rms():
    
    connection = pyodbc.connect(connection_string_rms)
    cursor = connection.cursor()
    rows= None
    try:
        procedure_name = '[RMS].[FetchUserMaster]'
        cursor.execute(f"EXEC {procedure_name}")
        
        print(f"{procedure_name}")
        rows = cursor.fetchall()
        
        cursor.close()
        connection.close()
        getAttendanceFrom_matrix(rows)

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("success")

execute_attendance_task = PythonOperator(
    task_id='execute_attendance_task',
    python_callable=fetchusersfrom_rms,
    dag=dag,
)

execute_attendance_task

