from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum

matrix_server=Variable.get("common_db_server")
rms_server= Variable.get("rms_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
rms_db= Variable.get("dbRMS")
rms_user=Variable.get("rms_username")
# rmsnew_pass= Variable.get("rmsnew_pass")
rms_pwd= Variable.get("rms_password")

default_args = {
    'owner': 'VISTA',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 9, 25, tz="Asia/Kolkata"),
    'retries': 1,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'execute_agentdatadump_dag_rms',
    default_args=default_args,
    #schedule_interval=timedelta(days=15), 
        
     schedule_interval='0 23 * * *',
    catchup=False,
)

def insertagentsdata_rms(rows):
     connection_string = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={rms_server};DATABASE={rms_db};UID={rms_user};PWD=*********"
     connection = pyodbc.connect(connection_string)
     cursor= connection.cursor()

     try:
         table_name="[RMS].[UserMaster]"
         update_isActive=f"Update {table_name} set IsActive=?"
         IsActive=False
         update=cursor.execute(update_isActive,IsActive)
        #  print(update)
         connection.commit()
         procedure_name="[RMS].[InsertAgentData]"
         for row in rows:
             
             params={
                    'UserId':row.UserId,
                    'EmployeeId': row.EmployeeId,
                    'EmployeeName':row.EmployeeName,
                    'ManagerId':row.ManagerId,
                    'ManagerEmployeeId':row.ManagerEmployeeId,
                    'ManagerName':row.ManagerName,
                    'ProcessId': row.ProcessId,
                    'ProcessName':row.ProcessName,
                    'GroupId':row.GroupId,
                    'GroupName': row.GroupName,
                    'ProductId':row.ProductId,
                    'UserType':row.UserType,
                    'IsActive': row.IsActive,
                    'CreatedOn':row.CreatedOn,
                    'ApplicationId': row.ApplicationId,
                    'StateId': row.StateId,
                    'RoleId':row.RoleId
                    }
             insertparams= (params['UserId'],params['EmployeeId'],params['EmployeeName'],params['ManagerId'],
                            params['ManagerEmployeeId'],params['ManagerName'],params['ProcessId'],params['ProcessName'],
                            params['GroupId'],params['GroupName'],params['ProductId'],params['UserType'],params['IsActive'],
                            params['CreatedOn'],params['ApplicationId'],params['StateId'],params['RoleId'])
             
             cursor.execute(f"EXEC {procedure_name} @UserId=?, @EmployeeId=?, @EmployeeName=?, @ManagerId=?, @ManagerEmployeeId=?, "
                            "@ManagerName=?, @ProcessId=?, @ProcessName=?, @GroupId=?, @GroupName=?, @ProductId=?, @UserType=?, "
                            "@IsActive=?, @CreatedOn=?, @ApplicationId=?, @StateId=?, @RoleId=? ", insertparams)
             connection.commit()
         
     except pyodbc.Error as e:
        print("Error:", e)

     finally:
             cursor.close()
             connection.close()

def fetchagentsdata_matrix():

    connection_string = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={matrix_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

    # Create a connection to the database
    connection = pyodbc.connect(connection_string)
    cursor = connection.cursor()
    rows= None
    try:
        
        procedure_name = '[MTX].[GetAgentsDataForRMS]'
        cursor.execute(f"EXEC {procedure_name}")
        
        # print(f"{procedure_name}")
        rows = cursor.fetchall()
        # for row in rows:
                # print(row)
        
        cursor.close()
        connection.close()
        insertagentsdata_rms(rows)

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("success")

execute_agentdatadump_task = PythonOperator(
    task_id='execute_agentdatadump_task',
    python_callable=fetchagentsdata_matrix,
    dag=dag,
)

execute_agentdatadump_task

