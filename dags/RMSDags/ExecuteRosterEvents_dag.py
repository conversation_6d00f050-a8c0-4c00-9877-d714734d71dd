import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum


default_args = {
    'owner': 'VISTA',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 10, 26, tz="Asia/Kolkata"),
    'retries': 1,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'ExeceuteRosterEvents_dag',
    default_args=default_args,
    schedule_interval="3 * * * *",  
    catchup=False,
)

def rosterevents_exec():
     #connection_string = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={rms_server};DATABASE={rms_db};UID={rms_user};PWD=*********"
     connection_string = ConnectionString(150,"RMS")
     #print(connection_string)
     #print("connection string "+connection_string)

     try:
        proc_name= '[Rms].[ExecuteRosterEvents]'
        connection = pyodbc.connect(connection_string)
        cursor= connection.cursor()
        cursor.execute(f"EXEC {proc_name}")
        row= cursor.fetchone()
        if(row):
             status= row.STATUS
             EventStatus=row.EventStatus
             EventId= row.EventId
             print(f"Status is {status}, EventStatus is {EventStatus} , EventId is {EventId}")
        else:
             print("no return")
        connection.commit()

     except pyodbc.OperationalError as e:
        print("OperationalERROR:",e)
        
     except pyodbc.Error as e:
        print("Error:", e)
   
     except Exception as e:
        print("Exception:", e)

     finally:
        cursor.close()
        connection.close()

execute_rosterEvents_task = PythonOperator(
    task_id='execute_rosterEvents_task',
    python_callable=rosterevents_exec,
    dag=dag,
)

execute_rosterEvents_task