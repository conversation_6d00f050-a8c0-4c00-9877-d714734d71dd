import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum

default_args = {
    'owner': 'MATRIX',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 4, 4, tz="Asia/Kolkata"),
    'retries': 1,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'ChurnLeads_dag',
    default_args=default_args,
    schedule_interval= "0 6 * * *",  
    catchup=False,
)


connection_string_matrix= ConnectionString(30,"Matrix_Primary")

def exec_proc():

     try:
        proc_name= '[MTX].[Dump-3-Day-LoginChurnLeads]'
        connection = pyodbc.connect(connection_string_matrix)
        cursor= connection.cursor()
        cursor.execute(f"EXEC {proc_name}")
        connection.commit()

     except pyodbc.OperationalError as e:
        print("OperationalERROR:",e)
        
     except pyodbc.Error as e:
        print("Error:", e)
   
     except Exception as e:
        print("Exception:", e)

     finally:
        cursor.close()
        connection.close()

ChurnLeads_task = PythonOperator(
    task_id='ChurnLeads_task',
    python_callable=exec_proc,
    dag=dag,
)

ChurnLeads_task