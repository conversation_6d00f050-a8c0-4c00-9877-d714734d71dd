import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import pendulum
import pyodbc
from pymongo import MongoClient
from airflow.providers.mongo.hooks.mongo import MongoHook

# Default arguments for the DAG
default_args = {
    'owner': 'Pankaj Rathour',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 3, 25, tz="Asia/Kolkata"),
    'retries': 0,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False
}

# DAG definition
dag = DAG(
    'UserRealTimeFreshAppointment',
    default_args=default_args,
    schedule_interval='*/9 8-21 * * *',  # Every 5 minutes from 8 AM to 9:59 PM
    catchup=False,
    max_active_runs=1,
)

# Fetch FOS Users
def getFOSUsers():
    connection_string = ConnectionString(150, "Matrix")
    try:
        proc_name = '[FOS].[GetFOSUsers]'
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        cursor.execute(f"EXEC {proc_name}")
        rows = cursor.fetchall()
        return rows
    except pyodbc.Error as e:
        print("Database Error:", e)
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
    


# Fetch Appointment Data EmpData.UserId
def getAppointmentByUser(EmpData):
    connection_stringRMS = ConnectionString(150, "Matrix")
    AppointmentList = []
    
    try:
            print("Running Appt data fetch...",EmpData)
            proc_name = '[FOS].[GetAppointmentsByUserId]'
            connection = pyodbc.connect(connection_stringRMS)
            cursor = connection.cursor()
            cursor.execute(f"EXEC {proc_name} @UserId=?, @SlotId=?, @LeadId=?", EmpData, 0, 0)
            AppointmentList = cursor.fetchall()
            
            if AppointmentList and len(AppointmentList) > 0:
                print(f"AppointmentList from method: {len(AppointmentList)} records.")
                return AppointmentList
            else:
                print("No AppointmentList data found.")
                return []

    except Exception as e:
        print(f"Error fetching AppointmentList: {e}")
        return []  # Always return empty list on error.

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def convert_decimal_to_float(data):
    if isinstance(data, dict):
        return {k: convert_decimal_to_float(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_decimal_to_float(item) for item in data]
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data

# Dump data into MongoDB
def DumpInMongo(data, AppointmentData):
    try:
            
        print("Start Dumping")
        # print("Mongo_Data",mongo_data)
        # mongo_data = convert_decimal_to_float(mongo_data)
        hook = MongoHook(mongo_conn_id='matrix_dashboard', conn_id='matrix_dashboard')
        client = hook.get_conn()
        db = client.MatrixDashboard
        collection = db.RealTimePanelV2
        # Loop through each appointment object
        for appt in AppointmentData:
            now = datetime.now()
               # Calculate if appointment is within 2 hours
            is_within_2_hours = (
                appt.AppointmentDateTime is not None and
                now <= appt.AppointmentDateTime <= now + timedelta(hours=2)
            )
            mongo_data = {
                "Isfresh": appt.isFreshLead,
                "NextVisitAtRisk": is_within_2_hours
            }
            filter_criteria = {"AppointmentId": appt.AppointmentId}
            update_data = {
                    "$set": mongo_data,
                    "$setOnInsert": {"mongoupdatedon": datetime.now()}  # Use this instead of CreatedOn
            }

            result = collection.update_one(filter_criteria, update_data, upsert=False)
        # print("MongoDB operation:", "Inserted" if result.upserted_id else "Updated")
    except Exception as e:
        print("Exception in DumpInMongo:", e)


# Main operation
def MongoDataFetcfromFOS():
    try:
        usersData = getFOSUsers()
        print("usersData",len(usersData)) 
        AppointmentData =  []
        AppointmentData = getAppointmentByUser(56672)
        print(f"AppointmentData List After>>>>>",AppointmentData)
        # if usersData:
            
            # for EmpData in usersData:  
                # AppointmentData = getFOSDataByUserId(EmpData) or []
               
                # print(f"EmpData execution started>>>>>",EmpData.UserId)
                    
                      
                    

        DumpInMongo(56672, AppointmentData)
                # print("Exits AppointmentData")
    except Exception as e:
        print("Exception in MongoDataFetcfromFOS:", e)

# Define PythonOperator
UserRealTimeFreshAppointment = PythonOperator(
    task_id='UserRealTimeFreshAppointment',
    python_callable=MongoDataFetcfromFOS,
    dag=dag,
)
