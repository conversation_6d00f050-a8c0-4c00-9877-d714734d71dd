from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, time, timezone, timedelta
import pyodbc
import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import base64


server=Variable.get("common_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
matrix_core_url = Variable.get("MATRIXCORE_URL")
mrs_core_url = Variable.get("mrs_core_url")
matrix_client = Variable.get("MATRIXCORE_CLIENT_KEY")
matrix_auth = Variable.get("MATRIXCORE_AUTH_KEY")
Matrix_AuthKey = Variable.get("Matrix_AuthKey")
Matrix_ClientKey = Variable.get("Matrix_ClientKey")
dialer_url = Variable.get("DIALER_URL")
dialer_client = Variable.get("DIALER_CLIENT_KEY")
dialer_auth = Variable.get("DIALER_AUTH_KEY")
dialer_AES_Key = Variable.get("DIALER_AES_KEY")
dialer_AES_IV = Variable.get("DIALER_AES_IV")


default_args = {
    'owner': 'Ayush Garg',
    'depends_on_past': False,
    'start_date': datetime(2025, 6, 24),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'AutoRecording_dag',
    default_args=default_args,
    schedule_interval='*/3 * * * *',
   #  schedule_interval= None,
    catchup=False,
   max_active_runs=1,
)


# connection_string = f'***************************************************************************************************************************'
connection_string_matrix=f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

def encryption(text):
    # AES_KEY = dialer_AES_Key  # Must be exactly 32 bytes
    # AES_IV  = dialer_AES_IV  # Must be exactly 16 bytes
    AES_KEY = dialer_AES_Key.encode('utf-8') if isinstance(dialer_AES_Key, str) else dialer_AES_Key  # Must be exactly 32 bytes
    AES_IV = dialer_AES_IV.encode('utf-8') if isinstance(dialer_AES_IV, str) else dialer_AES_IV  # Must be exactly 16 bytes
    
    try:
        # Create cipher object
        cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
        
        # Pad the text to be a multiple of 16 bytes (AES block size)
        padded_text = pad(text.encode('utf-8'), AES.block_size)
        
        # Encrypt the text
        encrypted_text = cipher.encrypt(padded_text)
        
        # Double Base64 encode as in the Java code
        return base64.b64encode(
            base64.b64encode(encrypted_text)
        ).decode('utf-8')
        
    except Exception as e:
        print(f"Unable to encrypt message: {text}, error: {str(e)}")
        raise

# def decryption(text):
#     AES_KEY = b'09BACG81E7D26SKR9O40FA2YD3CB6EAK'  # Must be exactly 32 bytes
#     AES_IV  = b'4DAS3GKAY0C864D1'             # Must be exactly 16 bytes
#     cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
#     decrypted_text = unpad(cipher.decrypt(base64.b64decode(text)), AES.block_size)
#     return decrypted_text.decode()

def recordingApi(rows):
    try:
        for row in rows:
            try:
                print("row", row)
                print("EmployeeId", row.EmployeeId)
                print("UserId", row.UserId)
                
                # Encrypt EmployeeId
                encrypted_EmployeeId = encryption(str(row.EmployeeId))
                print("encrypted_EmployeeId", encrypted_EmployeeId)
                
                # First API call to check agent status
                try:
                    api_url = f'https://internalcustomernotification.policybazaar.com/customer/getagenstatus/{row.UserId}'
                    headers = {
                        'source': 'fosapp', 
                        'clientKey': matrix_client, 
                        'authKey': matrix_auth,
                        'AgentId': str(row.UserId)  # Convert to string for header
                    }
                    agent_status_response = requests.get(api_url, headers=headers, timeout=30)
                    agent_status_data = agent_status_response.json()  # Parse the JSON response
                    print("Agent status response:", agent_status_data)

                    # Get status from JSON response body
                    response_status = agent_status_data.get('status')
                    print("Response status from JSON:", response_status)

                    if (response_status != 200):
                        try:
                            # Prepare and make authentication token request
                            body = {
                                "customerId": encrypted_EmployeeId,
                                "source": "matrixgo"
                            }
                            api_url = f'{dialer_url}/api/v2/pbapp/getAuthTokens'
                            headers = {
                                'source': 'matrixgo',
                                'authKey': dialer_auth,
                                'clientKey': dialer_client,
                                'Content-Type': 'application/json'
                            }

                            print("Api request getAuthTokens")
                            response = requests.post(api_url, headers=headers, json=body, timeout=30)
                            response.raise_for_status()
                            response_data = response.json()
                            print("Auth token response:", response_data)
                                
                            if response_data.get('status') == 200 and response_data.get('data', {}).get('token'):
                                token = response_data['data']['token']
                                print("Token received successfully", token)
                                    
                                # Prepare and make the call
                                try:
                                    # 2. Get Call ID
                                    callIdData = {
                                        "ParentID": str(getattr(row, 'LeadID', '')),
                                        "ProductID": str(getattr(row, 'ProductID', '')),
                                        "EmpCode":  str(row.EmployeeId),
                                        "UserId" : '124'
                                    }
                
                                    call_id_url = f'{matrix_core_url}fos/GetCallId'
                                    call_id_headers = {
                                        'AgentId' : '124',
                                        'source': 'fosapp',
                                        'clientKey': Matrix_ClientKey, 
                                        'authKey': Matrix_AuthKey
                                    }

                                    print(f"Requesting call ID for LeadID: {row.LeadID}")
                                    call_id_response = requests.post(
                                        call_id_url, 
                                        headers=call_id_headers, 
                                        json=callIdData, 
                                        timeout=30
                                    )
                                    call_id_response.raise_for_status()
                                    print("Call ID response:", call_id_response.json())
                                    call_id_data = call_id_response.json()  # Parse the JSON response
                                    call_id = call_id_data.get('CallId')
                                    print(f"Call ID response - Status: {call_id_data.get('status')}, Call ID: {call_id}")

                                    if call_id:
                                        call_data = {
                                            "uid" : str(call_id),
                                            "customerId": encrypted_EmployeeId,
                                            "source": "matrixgo",
                                            "agentId": str(row.EmployeeId),
                                            "leadId": str(getattr(row, 'LeadID', '')), # Using getattr for safety
                                            "agentPhone": str(row.DIDNO)
                                        }
                                
                                        api_url = f'{dialer_url}/api/v2/pbapp/makeCallV2'
                                        headers = {
                                            'source': 'matrixgo',
                                            'authKey': dialer_auth,
                                            'clientKey': dialer_client,
                                            'Content-Type': 'application/json',
                                            'token': str(token)
                                        }
                                
                                        print("call_data", call_data)
                                        print("api_url", api_url)
                                        print("headers", headers)

                                        call_response = requests.post(
                                            api_url, 
                                            headers=headers, 
                                            json=call_data, 
                                            timeout=30
                                        )
                                        call_response.raise_for_status()
                                        print("Call initiated successfully:", call_response.json())
                                        
                                except requests.exceptions.RequestException as e:
                                    print(f"Error in making call: {str(e)}")
                                except ValueError as e:
                                    print(f"Error in parsing call response: {str(e)}")
                                        
                        except requests.exceptions.RequestException as e:
                            print(f"Error in authentication API: {str(e)}")
                        except ValueError as e:
                            print(f"Error in parsing authentication response: {str(e)}")
                            
                except requests.exceptions.RequestException as e:
                    print(f"Error in agent status API: {str(e)}")
                    
            except Exception as e:
                print(f"Error in processing row: {str(e)}")
                continue  # Continue with next row even if one fails
                
    except Exception as e:
        print(f"Critical error in insertprocesssdata_rms: {str(e)}")
        raise  # Re-raise to handle in the outer try-catch

    # finally:
    #     cursor.close()
    #     connection.close()

def is_within_time_window():
    """Check if current time is between 8 AM and 10 PM IST (UTC+5:30)"""
    try:
        # Get current time in UTC
        utc_now = datetime.now(timezone.utc)
        
        # Convert to IST (UTC+5:30)
        ist_offset = timedelta(hours=5, minutes=30)
        now_ist = utc_now + ist_offset
        
        # Define time window in IST
        start_time = time(8, 0)  # 8 AM IST
        end_time = time(22, 0)   # 10 PM IST
        
        # Get current time in IST (without timezone)
        current_time_ist = now_ist.time()
        
        # Format the current time as a string
        current_time_str = now_ist.strftime("%Y-%m-%d %H:%M:%S IST")
        print(f"Current IST time: {current_time_str}")
        
        # Check if within time window
        is_within = start_time <= current_time_ist <= end_time
        print(f"Within 8 AM - 10 PM IST: {'Yes' if is_within else 'No'}")
        
        return is_within
    except Exception as e:
        print(f"Error in is_within_time_window: {str(e)}")
        return False

def getDataforCalling():
    # Check if current time is within the allowed window (8 AM - 10 PM)
    if not is_within_time_window():
        print("Current time is outside the allowed window (8 AM - 10 PM). Exiting.")
        return
        
    connection = None
    cursor = None
    try: 
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()

        # Split the SQL into individual statements
        sql_statements = [
            """
            USE PBCROMA;
            DROP TABLE IF EXISTS #AppData;
            DROP TABLE IF EXISTS #CallData;
            
            CREATE TABLE #AppData (
                LeadID BIGINT,
                UserId BIGINT,
                CustomerID BIGINT,
                EmployeeId VARCHAR(50),
                StatusCreatedOn DATETIME,
                CallCount INT,
                DIDNO BIGINT,
                IsActive BIT,
                ProductID INT
            );
    
            CREATE TABLE #CallData (
                LeadID BIGINT,
                CallType VARCHAR(50),
                CallDate DATETIME,
                CallId VARCHAR(100),
                UpdatedOn DATETIME
            );
            """,
            """
            INSERT INTO #AppData(LeadID,UserId,CustomerId,EmployeeId,StatusCreatedOn,CallCount,DIDNO,IsActive,ProductID)
            SELECT  s.LeadID, s.UserId ,d.CustomerID, u.EmployeeId,s.CreatedOn,0,u.DIDNo,1,LD.ProductId
            FROM    MTX.AppointmentData(nolock) d
            JOIN    mtx.AppointmentSubStatus s(nolock)  ON d.Id=s.AppointmentID AND s.isLastStatus=1
            AND     s.CreatedOn >= DATEADD(MINUTE, -60, GETDATE())
            JOIN    crm.UserDetails u(nolock) ON u.UserID = s.UserId
            JOIN    FOS.AppointmentsHistory(nolock) h ON s.AppointmentID = h.AppointmentId and s.isLastStatus=1
            INNER JOIN [Matrix].[CRM].[leaddetails150] LD (NOLOCK) ON LD.LeadID = s.Leadid
            AND     h.EventType = 73
            AND     h.Comments = 'YES'
            WHERE   d.IsActive=1 AND s.SubStatusId = 2124 AND LD.ProductId = 2 AND LD.LeadSource NOT LIKE '%Renewal%'
            """,
            """
            INSERT INTO #CallData(LeadID,CallType,CallDate,CallId,UpdatedOn)
            SELECT      CDh.Leadid,CDH.CallType,CDH.CallDate,CallID,UpdatedOn
            FROM        #AppData A
            INNER JOIN  mtx.CallDataHistory(nolock) CDH on CDH.LeadID=A.LeadID  and CallType='FOSVISIT'
            AND         CDH.CallDate > CAST(GETDATE() AS DATE)
            WHERE       A.IsActive=1
            """,
            """
            Update A
            SET CallCount=T.CallCount
            FROM #AppData A
            INNER JOIN 
            (

                SELECT CDH.LeadID,Count(1) as CallCount
                from #AppData A
                INNER JOIN #CallData(nolock) CDH on CDH.LeadID=a.LeadID  and CallType='FOSVISIT' and cdh.CallDate>a.StatusCreatedOn 
                WHERE IsActive=1
                GROUP by CDH.LeadID
            ) T on T.LeadID=A.LeadID
            """,
            """
            Update  A
            SET     IsActive=0
            FROM    #AppData A
            WHERE   CallCount > 3
            """,
            """
            Update A
            SET IsActive=0
            FROM #AppData A
            INNER JOIN 
            (
                SELECT CDH.LeadID
                from #AppData A
                INNER JOIN #CallData(nolock) CDH on CDH.LeadID=a.LeadID  and CallType='FOSVISIT' and cdh.UpdatedOn IS NULL AND CallID = '0'
                WHERE IsActive=1
            ) T on T.LeadID=A.LeadID
            """
        ]

        # Execute all statements except the last one (which is the SELECT)
        for stmt in sql_statements:
            cursor.execute(stmt)
            connection.commit()

        # Execute the final SELECT query and fetch results
        select_query = "SELECT * from #AppData WHERE IsActive=1"

        cursor.execute(select_query)
        rows = cursor.fetchall()
        
        if rows:
            recordingApi(rows)
        else:
            print("No records found matching the criteria.")

    except pyodbc.Error as e:
        print(f"Database error in getDataforCalling: {str(e)}")
        raise
    except Exception as e:
        print(f"Unexpected error in getDataforCalling: {str(e)}")
        raise
    finally:
        try:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        except Exception as e:
            print(f"Error closing database connection: {str(e)}")



AutoRecording = PythonOperator(
    task_id='AutoRecording_dag',
    python_callable=getDataforCalling,
    dag=dag,
)
 
AutoRecording
