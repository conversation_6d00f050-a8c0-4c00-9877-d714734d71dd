# from deepface import DeepFace


# # Paths to local images
# # selfie_path = "selfie.jpg"  # Replace with your actual file path
# selfie_path = "C:\\Users\\<USER>\\Downloads\\PIcComapre\\gunjan_2.jpeg" 
# profile_pic_path = "C:\\Users\\<USER>\\Downloads\\PIcComapre\\gunjan_1.jpeg"  


# # profile_pic_path = "profile_picture.jpg"  # Replace with your actual file path
# # print('Get Images',selfie_path,profile_pic_path)


# # # Compare faces
# result = DeepFace.verify(selfie_path, profile_pic_path)


# # Print the result
# print("Face Comparison Result:", result)


# # Extract important details
# if result["verified"]:
#     print("The images belong to the same person.")
# else:
#     print("The images do NOT match.")


# print(f"Distance: {result['distance']:.4f} (Lower is better)")
# print(f"Threshold: {result['threshold']:.4f}")
# print(f"Model Used: {result['model']}")
