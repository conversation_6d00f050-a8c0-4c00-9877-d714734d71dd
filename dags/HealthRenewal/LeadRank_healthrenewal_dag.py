from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc
import pendulum

server = Variable.get("common_db_server")
live_server = Variable.get("primary_matrixdb")
matrix_db = Variable.get("dbmatrix")
matrix_user = Variable.get("matrix_username")
matrix_pwd = Variable.get("matrix_password")

default_args = {
    "owner": "<PERSON><PERSON><PERSON>",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 1, 1, tz="Asia/Kolkata"),
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

dag = DAG(
    "LeadRank_healthrenewal_dag_new",
    default_args=default_args,
    schedule_interval="0 0 1,3 * *", # Run on the 1st and 3rd of every month at midnight
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"],
)

connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"
live_connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={live_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"


def setleadrank(lead_list):

    connection = pyodbc.connect(live_connection_string_matrix)
    cursor = connection.cursor()
    try:
        for lead in lead_list:
            if not lead or not lead[0]:
                continue

            lead_id = lead[0]
            print("lead_id", lead_id)
            procedure_name = "[MTX].[Retention_Lead_Rank_HealthRen]"
            cursor.execute(f"EXEC {procedure_name} ?", (lead_id))
            connection.commit()
        cursor.close()
        connection.close()
    except pyodbc.Error as e:
        print("Error:", e)


def getLeadsFromMatrix():
    connection = pyodbc.connect(connection_string_matrix)
    cursor = connection.cursor()

    try:
        print("try")
        procedure_name = "[MTX].[Retention_Leads_HealthRen]"
        cursor.execute(f"EXEC {procedure_name}")
        leads = cursor.fetchall()
        cursor.close()
        connection.close()
        setleadrank(leads)

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("finally")


retention_leadrank_healthrenewal = PythonOperator(
    task_id="retention_leadrank_healthrenewal",
    python_callable=getLeadsFromMatrix,
    provide_context=True,
    dag=dag,
)

retention_leadrank_healthrenewal
