from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc
import requests

server = Variable.get("common_db_server")
live_server = Variable.get("primary_matrixdb")
matrix_db = Variable.get("dbmatrix")
matrix_user = Variable.get("matrix_username")
matrix_pwd = Variable.get("matrix_password")

default_args = {
    'owner': 'Nandini Garg',
    'depends_on_past': False,
    'start_date': datetime(2023, 12, 13),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'Renewal_AssignedToMultipleAgent_dag',
    default_args=default_args,
    schedule_interval="*/5 * * * *", # Runs every 5 minutes
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"]
)

connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"
live_connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={live_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

def assignleads(lead_list):

    connection = pyodbc.connect(live_connection_string_matrix)
    cursor = connection.cursor()
    uniqueid = ''
    try:
        for lead in lead_list:
            print(lead)
            if not lead or not lead[2]:
                continue

            LeadId = lead[2]
            AssignedBy_AgentId = lead[5]
            AssignedTo_AgentId = lead[3]
            JobId = lead[7]
            ProductId = lead[6]
            uniqueid = lead[10]
            procedure_name = "[CRM].[Insert_AssignedToAgent]"
        
            insertparams = (AssignedTo_AgentId, AssignedBy_AgentId, ProductId, LeadId, JobId)
            cursor.execute(f"EXEC {procedure_name} @AssignedTo_AgentId=?, @AssignedBy_AgentId=?, @ProductId=?, @LeadId=?, @JobId=?", insertparams)
            connection.commit()
        updateleads(uniqueid)
    except pyodbc.Error as e:
        print("Error:", e)
    finally:
        print("finally assignleads")
        cursor.close()
        connection.close()
        
def updateleads(uniqueid):

    connection = pyodbc.connect(live_connection_string_matrix)
    cursor = connection.cursor()
    try:
        cursor.execute(f"UPDATE [MTX].[LeadsReassignment] SET IsAssigned = 1 WHERE UniqueId = ?", uniqueid)
        connection.commit()
        
        
    except pyodbc.Error as e:
        print("Error:", e)
    finally:
        print("finally updateleads")
        cursor.close()
        connection.close()

def getLeadsFromMatrix():
     
    connection = pyodbc.connect(connection_string_matrix)
    cursor= connection.cursor()

    try: 
        procedure_name="[MTX].[Fetch_Update_AssignedToMultipleAgent]"
        cursor.execute(f"EXEC {procedure_name} @Type = 2")
        leads = cursor.fetchall()

        cursor.close()
        connection.close()
        assignleads(leads)

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("finally getLeadsFromMatrix")

Renewal_AssignedToMultipleAgent = PythonOperator(
    task_id='Renewal_AssignedToMultipleAgent',
    python_callable=getLeadsFromMatrix,
    provide_context=True,
    dag=dag,
)

Renewal_AssignedToMultipleAgent