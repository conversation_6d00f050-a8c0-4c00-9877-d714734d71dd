from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc
import requests

server=Variable.get("common_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
matrix_core_url = Variable.get("MATRIXCORE_URL")
mrs_core_url = Variable.get("mrs_core_url")
matrix_client = Variable.get("MATRIXCORE_CLIENT_KEY")
matrix_auth = Variable.get("MATRIXCORE_AUTH_KEY")

default_args = {
    'owner': 'Nandini Garg',
    'depends_on_past': False,
    'start_date': datetime(2023, 12, 13),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'datapush_healthRenewal_dag',
    default_args=default_args,
    schedule_interval="*/10 * * * *", # Run every 10 minutes
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"]
)

connection_string_matrix=f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

def apiToPushLeadsData(lead_list):

    for lead in lead_list:
        if not lead or not lead[0]: 
            continue

        lead_id = lead[0]
        print("lead_id",lead_id)
        api_url = f'{matrix_core_url}LeadDetails/PushRenewalLeadToCJ/{lead_id}'
        headers = {'source': 'airflow', 'clientKey': matrix_client, 'authKey': matrix_auth}
        response = requests.get(api_url, headers=headers)
        
        api_url = f'{mrs_core_url}coremrs/api/ProposalService/UpdateUpsellLead/{lead_id}'
        headers = {'source': 'airflow', 'clientKey': matrix_client, 'authKey': matrix_auth}
        response = requests.get(api_url, headers=headers)
        
        api_url = f'{matrix_core_url}HealthRenewal/SetInceptionBookingID/{lead_id}'
        headers = {'source': 'airflow', 'clientKey': matrix_client, 'authKey': matrix_auth}
        response = requests.get(api_url, headers=headers)

def getLeadsFromMatrix():
     
    connection = pyodbc.connect(connection_string_matrix)
    cursor= connection.cursor()

    try: 
        procedure_name="[MTX].[GetPendingLeadsForPushToCJ_sch]"
        cursor.execute(f"EXEC {procedure_name}")
        leads = cursor.fetchall()

        cursor.close()
        connection.close()
        apiToPushLeadsData(leads)

    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("finally")

push_leaddata_to_healthRenewalCJ = PythonOperator(
    task_id='push_leaddata_to_healthRenewalCJ',
    python_callable=getLeadsFromMatrix,
    provide_context=True,
    dag=dag,
)

push_leaddata_to_healthRenewalCJ