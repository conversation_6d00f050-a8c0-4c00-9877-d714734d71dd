from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pyodbc

server=Variable.get("common_db_server")
matrix_db= Variable.get("dbmatrix")
matrix_user= Variable.get("matrix_username")
matrix_pwd= Variable.get("matrix_password")
live_server = Variable.get("primary_matrixdb")

default_args = {
    'owner': 'Nandini Garg',
    'depends_on_past': False,
    'start_date': datetime(2023, 12, 13),
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'stu_assignment_healthRenewal_dag',
    default_args=default_args,
    schedule_interval="*/10 8-22 * * *", # Run every 10 minutes from 8 AM to 10 PM
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"]
)

connection_string_matrix=f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={server};DATABASE={matrix_db};UID={matrix_user};PWD=************"
live_connection_string_matrix = f"DRIVER=ODBC Driver 17 for SQL Server;SERVER={live_server};DATABASE={matrix_db};UID={matrix_user};PWD=************"

def assignleads(lead_list):

    connection = pyodbc.connect(live_connection_string_matrix)
    cursor = connection.cursor()
    uniqueid = ''
    try:
        for lead in lead_list:
            print(lead)
            if not lead or not lead[3]:
                continue

            LeadId = lead[3]
            AssignedTo_AgentId = lead[2]
            procedure_name = "[CRM].[Insert_AssignedToAgent]"
        
            insertparams = (AssignedTo_AgentId, LeadId)
            cursor.execute(f"EXEC {procedure_name} @AssignedTo_AgentId=?, @AssignedBy_AgentId=124, @ProductId=130, @LeadId=?, @JobId=20", insertparams)
            connection.commit()
            
    except pyodbc.Error as e:
        print("Error:", e)
    finally:
        print("finally assignleads")
        cursor.close()
        connection.close()
        
def getLeadsFromMatrix():
     
    connection = pyodbc.connect(connection_string_matrix)
    cursor= connection.cursor()

    try: 
        procedure_name="[MTX].[STU_AutoAllocation_Renewal]"
        cursor.execute(f"EXEC {procedure_name}")
        leads = cursor.fetchall()

        cursor.close()
        connection.close()
        assignleads(leads)
    except pyodbc.Error as e:
        print("Error:", e)

    finally:
        print("finally")

renewal_stu_assignment = PythonOperator(
    task_id='renewal_stu_assignment',
    python_callable=getLeadsFromMatrix,
    provide_context=True,
    dag=dag,
)

renewal_stu_assignment