from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import timedelta
import pendulum
import requests

default_args = {
    "owner": "<PERSON><PERSON><PERSON>",
    "depends_on_past": False,
    "start_date": pendulum.datetime(2024, 10, 1, tz="Asia/Kolkata"),
    "email": ["<EMAIL>"],
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

dag = DAG(
    "chat_missed_trigger",
    default_args=default_args,
    schedule_interval="*/5 8-19 * * *", # Run every 5 minutes from 8 AM to 7 PM
    catchup=False,
    max_active_runs=1,
    tags=["Matrix"],
)


def chat_trigger():
    print("Chat Missed Trigger")
    api_url = "http://10.80.25.89/communication/Asteriskevent.svc/TriggerCommunication?ProductID=2&SPName=[MTX].[SendUnavailableTrigger]"
    response = requests.get(api_url)


chat_missed_trigger = PythonOperator(
    task_id="chat_missed_trigger",
    python_callable=chat_trigger,
    provide_context=True,
    dag=dag,
)

chat_missed_trigger
