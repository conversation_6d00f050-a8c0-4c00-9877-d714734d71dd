import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum
import requests
import json
from datetime import datetime
from airflow.providers.mongo.hooks.mongo import MongoHook
from datetime import timedelta
import hashlib

secret_key = Variable.get("rms_RamcoSecKey")
access_key = Variable.get("rms_RamcoAccessKey")


default_args = {
    'owner': 'JAG',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2023, 10, 26, tz="Asia/Kolkata"),
    'retries': 0,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
}

dag = DAG(
    'execute_BookingUpdate_task',
    default_args=default_args,
    schedule_interval=timedelta(days=7),
    catchup=False,
)

def SaveLogs(Method,Application,TrackingID,RequestText,response,Exception):
    try:
#      print("Start MongoInertion")
     data = {
     "Application": Application,
     "Method": Method,
     "TrackingID": TrackingID,
     "RequestText": RequestText,
     "ResponseText": response,
     "Requesttime": datetime.now(),
     "Responsetime": datetime.now(),
     "Exception": Exception,
     "CreatedOn": datetime.now(),
     "CreatedBy": "Automation"
     }
#      print("data prepare")
     hook = MongoHook(mochngo_conn_id='logger', conn_id='logger')
     client = hook.get_conn()
     db = client.logger
     collection = db.Log_Collection  
     collection.insert_one(data)       
       
#      print("Document inserted successfully.")

    except requests.exceptions.RequestException as e:
     print("Request failed:", e)
    except Exception as e:
     print("Exception in StateUpdateSaveLogs:", e) 


def getPrimaryBookingDetails(EmployeeId):

   try:
     payload  = {
                "AgentIDs": ["37819"],
                "BookingType": 1,
                "FromDate": "2025-06-01",
                "ToDate": "2025-07-01",
                "ProductIds": [2],
                "ProductPresent": 1,
                "LeadIDs": [],
                "Isadvisor": True
               }
     
     api_url = 'https://bmscromaapi.policybazaar.com/api/Matrix/GetMyBookingDetails'
     headers = {'Content-Type': 'application/json',
                'clientkey':'qLIjrnvOSI',
                'authKey':'b63phurQkK7P2LImTZS',
                'source': 'matrix' } 
     
     timeout_seconds = 30     
    #  Logs += " -payload: " + str(payload) + "\n"

     response = requests.post(api_url, headers = headers, json = payload, timeout= timeout_seconds )
     if response.status_code == 200:
            response_json = response.json()
            
            # Optional: Print the full response for debugging
            print("Full response:", json.dumps(response_json, indent=2))
            
            # Print MyBookingList specifically
            booking_list = response_json.get("MyBookingList", [])
            print("\n--- MyBookingList ---")
            print(json.dumps(booking_list, indent=2))

     else:
    # Print an error message if the request failed
      print(f"Primary Request failed with status code:", response.status_code)
      print(f"Primary Request failed with EmpId-:", EmployeeId)
      getSecondryBookingDetails("PW00720")

    #  jsonResp = response.json() 
    #  json_string = json.dumps(jsonResp)
    #  response_dict = json.loads(json_string)
    #  if "ReturnData" in response_dict and response_dict["ReturnData"]:
    #     first_element = response_dict["ReturnData"][0]
    #     state_id = first_element["StateID"]

    #  UpdateDataIntoDb(EmployeeId, state_id)
#      SaveLogs("getEmployeePIDetails","Airflow",124,Logs,response," ")
     
   except Exception as e:
     print("Exception-:", e)



def getSecondryBookingDetails(EmployeeId):

   try:
     payload  = {
                "AgentIDs": [   
                    37033,
                    38487,
                    38788,
                    44159,
                    50451,
                    53576,
                    61576,
                    62976,
                    63864,
                    64685,
                    67106,
                    71609,
                    72498,],
                "BookingType": 2,
                "FromDate": "2025-06-01",
                "ToDate": "2025-07-01",
                "ProductIds": [2],
                "ProductPresent": 1,
                "LeadIDs": [669192888],
                "Isadvisor": True
               }
     
     api_url = 'https://bmscromaapi.policybazaar.com/api/Matrix/GetMyBookingDetails'
     headers = {'Content-Type': 'application/json',
                'clientkey':'qLIjrnvOSI',
                'authKey':'b63phurQkK7P2LImTZS',
                'source': 'matrix' } 
     
     timeout_seconds = 30     
     Logs += " -payload: " + str(payload) + "\n"
    

     response = requests.post(api_url, headers = headers, json = payload, timeout= timeout_seconds )
     if response.status_code == 200:
            response_json = response.json()
            
            # Optional: Print the full response for debugging
            print("Full response:", json.dumps(response_json, indent=2))
            
            # Print MyBookingList specifically
            booking_list = response_json.get("MyBookingList", [])
            print("\n--- MyBookingList ---")
            print(json.dumps(booking_list, indent=2))

     else:
    # Print an error message if the request failed
      print(f"Secondry Request failed with status code:", response)
      print(f"Secondry Request failed with status code:", response.status_code)
      print(f"Secondry Request failed with EmpId-:", EmployeeId)


    #  jsonResp = response.json() 
    #  json_string = json.dumps(jsonResp)
    #  response_dict = json.loads(json_string)
    #  if "ReturnData" in response_dict and response_dict["ReturnData"]:
    #     first_element = response_dict["ReturnData"][0]
    #     state_id = first_element["StateID"]

    #  UpdateDataIntoDb(EmployeeId, state_id)
#      SaveLogs("getEmployeePIDetails","Airflow",124,Logs,response," ")
     
   except Exception as e:
     print("Exception-:", e)



def get_user_details():
    connection_string = ConnectionString(150, "Matrix")  # Your existing method
    query = "SELECT Top 3 AgentId ,EmployeeId,UserName,ProductId, RoleType, IncentiveMonth FROM  mtx.AgentProcessDetails(NOLOCK)"

    try:
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        cursor.execute(query)
        rows = cursor.fetchall()

        for row in rows:
            print(row)  # or return it

    except pyodbc.OperationalError as e:
        print("Operational ERROR:", e)
    except pyodbc.Error as e:
        print("DB ERROR:", e)
    except Exception as e:
        print("Unhandled Exception:", e)

    finally:
            connection.commit()
            cursor.close()
            connection.close()
            return row


def fnUpdateBookingData():
     try:
      usersData = get_user_details()
      print("usersData>>>:", usersData)
    #   if len(usersData)>0:
    #    for EmpData in usersData:
      getPrimaryBookingDetails('PW00720')     
     except Exception as e:
       print("Exception:", e)
   
execute_BookingUpdate_task = PythonOperator(
    task_id='execute_BookingUpdate_task',
    python_callable=fnUpdateBookingData,
    dag=dag,
)

execute_BookingUpdate_task