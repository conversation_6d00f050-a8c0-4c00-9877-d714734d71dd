import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.MatrixAPIConnection import IncentiveAPIUrl
from airflow import DAG
from airflow.operators.python_operator import Python<PERSON>perator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum
import requests 

default_args = {
    'owner': 'INCENTIVE',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 2, 12, tz="Asia/Kolkata"),
    'retries': 1,
    'email':['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'dailyAPECalculation',
    default_args=default_args,
    schedule_interval="0 1 * * *",
    catchup=False,
)

def callIncentiveAPI():
    try:
        
        api_url = IncentiveAPIUrl("api/Schedular/CalculateEuropeTripContest")
        print("apiURL: ", api_url)
        response = requests.get(api_url, timeout=300)
        print("Status Code ",response.status_code)
        print("Response ", response.json())
        CalculateASEANTripContest()

    except requests.exceptions.Timeout:
        print("The request timed out")

    except requests.exceptions.RequestException as e:
        print("An error occured: ",e)
    
    except Exception as e:
        print("Exception raised: ",e)


def CalculateASEANTripContest():
    try:
        api_url1 = IncentiveAPIUrl("api/Schedular/CalculateASEANTripContest")
        response1 = requests.get(api_url1, timeout=300)
        print("Status Code ",response1.status_code)
        # print("Response ", response.json())

    except requests.exceptions.Timeout:
        print("The request timed out")

    except requests.exceptions.RequestException as e:
        print("An error occured: ",e)
    
    except Exception as e:
        print("Exception raised: ",e)

dailyAPECalculation = PythonOperator(
    task_id='dailyAPECalculation',
    python_callable=callIncentiveAPI,
    dag=dag,
)

dailyAPECalculation
