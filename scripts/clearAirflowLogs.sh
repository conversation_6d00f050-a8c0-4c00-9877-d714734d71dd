#!/bin/bash

current_date=$(date +%F)
current_hour=$(date +%H)

n_days_ago=$(date -d "$current_date - 7 days" +%F)

echo "$(date): Starting log cleanup script"

# Airflow Logs Folder Deletion (runs every hour)
echo "$(date): Cleaning Airflow logs from $n_days_ago"
rm -rf "/home/<USER>/airflow/logs/scheduler/$n_days_ago"
rm -rf "/home/<USER>/airflow/logs/dag_id=*/*$n_days_ago*"

# Journal logs deletion (only at midnight - hour 00)
if [ "$current_hour" = "00" ]; then
    echo "$(date): Midnight detected - cleaning journal logs"
    rm -rf /var/log/journal/*/*
    echo "$(date): Journal logs cleanup completed"
else
    echo "$(date): Skipping journal logs cleanup (not midnight, current hour: $current_hour)"
fi

echo "$(date): Log cleanup script completed"