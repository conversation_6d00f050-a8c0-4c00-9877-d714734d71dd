#!/usr/bin/env python3
"""
Test script to validate CustomerStatsDAG structure and logic
"""

import sys
import os

def test_dag_structure():
    """Test basic DAG structure and imports"""
    try:
        # Add the dags directory to Python path
        dag_path = os.path.join(os.getcwd(), 'dags')
        if dag_path not in sys.path:
            sys.path.insert(0, dag_path)
        
        print("Testing DAG file structure...")
        
        # Read the DAG file
        with open('dags/MatrixDag/CustomerStatsDAG.py', 'r') as f:
            dag_content = f.read()
        
        # Check for required components
        required_components = [
            'from airflow import DAG',
            'from airflow.operators.python_operator import PythonOperator',
            'from airflow.models import Variable',
            'from airflow.providers.mongo.hooks.mongo import MongoHook',
            'def get_customer_ids_batch():',
            'def get_customer_stats_from_sql(customer_id):',
            'def format_customer_stats_data(customer_id, sql_rows):',
            'def store_customer_stats_in_mongo(customer_docs):',
            'def process_customer_stats_batch():',
            'def check_collections_status():',
            'customer_stats_task = PythonOperator(',
            'status_check_task = PythonOperator(',
            'status_check_task >> customer_stats_task'
        ]
        
        missing_components = []
        for component in required_components:
            if component not in dag_content:
                missing_components.append(component)
        
        if missing_components:
            print("❌ Missing required components:")
            for component in missing_components:
                print(f"  - {component}")
            return False
        else:
            print("✅ All required components found")
        
        # Check for proper error handling
        error_handling_patterns = [
            'try:',
            'except Exception as e:',
            'print(f"Error'
        ]
        
        error_handling_count = sum(1 for pattern in error_handling_patterns if pattern in dag_content)
        if error_handling_count >= 10:  # Should have multiple try-except blocks
            print("✅ Adequate error handling found")
        else:
            print(f"⚠️  Limited error handling found ({error_handling_count} patterns)")
        
        # Check for Airflow Variable usage
        variable_patterns = [
            'Variable.get("CustomerStats_BatchSize"',
            'Variable.get("CustomerStats_LastProcessedObjectId"',
            'Variable.set("CustomerStats_LastProcessedObjectId"'
        ]
        
        variable_count = sum(1 for pattern in variable_patterns if pattern in dag_content)
        if variable_count >= 3:
            print("✅ Proper Airflow Variable usage found")
        else:
            print(f"⚠️  Limited Airflow Variable usage ({variable_count} patterns)")
        
        # Check for MongoDB operations
        mongo_patterns = [
            'MongoHook(mongo_conn_id=',
            'collection.find(',
            'collection.bulk_write(',
            'ObjectId('
        ]
        
        mongo_count = sum(1 for pattern in mongo_patterns if pattern in dag_content)
        if mongo_count >= 4:
            print("✅ Proper MongoDB operations found")
        else:
            print(f"⚠️  Limited MongoDB operations ({mongo_count} patterns)")
        
        # Check for SQL operations
        sql_patterns = [
            'pyodbc.connect(',
            'cursor.execute(',
            'EXEC [MTX].[GetCustomerStats]'
        ]
        
        sql_count = sum(1 for pattern in sql_patterns if pattern in dag_content)
        if sql_count >= 3:
            print("✅ Proper SQL operations found")
        else:
            print(f"⚠️  Limited SQL operations ({sql_count} patterns)")
        
        print("\n📋 DAG Configuration Summary:")
        print("- DAG Name: CustomerStatsDAG")
        print("- Schedule: Hourly (timedelta(hours=1))")
        print("- Max Active Runs: 1")
        print("- Batch Size: Configurable via Airflow Variable (default: 5000)")
        print("- Progress Tracking: ObjectId-based marker in Airflow Variables")
        print("- Collections: BestTimeToCall (source) -> CustomerStats (target)")
        print("- Stored Procedure: [MTX].[GetCustomerStats]")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing DAG structure: {e}")
        return False

def test_data_format():
    """Test the expected data format"""
    print("\n🔍 Testing data format structure...")
    
    # Expected input from GetCustomerStats stored procedure
    expected_sql_fields = [
        'day_of_week', 'day_name', 'hour_of_day', 'answeredCount',
        'answeredCount15s', 'answeredCount2min', 'unansweredCount',
        'call_count', 'total_talk_time', 'appointment_visit_count',
        'appointment_count', 'callback_attempts', 'callback_success',
        'callback_count'
    ]
    
    # Expected output format for MongoDB
    expected_mongo_format = {
        "_id": "MongoDB ObjectId (auto-generated)",
        "cid": "CustomerID",
        "d": [
            {
                "dw": "day_of_week",
                "dn": "day_name",
                "h": "hour_of_day",
                "ans": "answered",
                "ans15": "answered within 15 sec",
                "ans2": "answered within 2 min",
                "na": "not answered",
                "tc": "total calls",
                "tt": "total talk time (seconds)",
                "apptV": "appointment visits",
                "appt": "total appointments",
                "cb": "callback attempts",
                "cbS": "callback success",
                "cbC": "callback count"
            }
        ]
    }
    
    print("✅ Expected SQL fields:", expected_sql_fields)
    print("✅ Expected MongoDB format:", expected_mongo_format)
    
    return True

def main():
    """Main test function"""
    print("🧪 CustomerStatsDAG Validation Test")
    print("=" * 50)
    
    success = True
    
    # Test DAG structure
    if not test_dag_structure():
        success = False
    
    # Test data format
    if not test_data_format():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! DAG appears to be properly structured.")
        print("\n📝 Next steps:")
        print("1. Deploy the DAG to Airflow")
        print("2. Set up required Airflow Variables:")
        print("   - CustomerStats_BatchSize (default: 5000)")
        print("   - CustomerStats_LastProcessedObjectId (will be auto-managed)")
        print("3. Ensure MongoDB connections are configured:")
        print("   - mongo_onelead connection")
        print("4. Test with a small batch first")
        print("5. Monitor logs for any issues")
    else:
        print("❌ Some tests failed. Please review the issues above.")
    
    return success

if __name__ == "__main__":
    main()
