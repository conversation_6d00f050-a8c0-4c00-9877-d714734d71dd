-- =============================================
-- Author: <PERSON><PERSON>
-- Create date: 15-07-2025
-- Description: GetCustomerCallingStats
-- =============================================
-- select top 100 * from mtx.CustomerCallStats
-- [MTX].[GetCustomerStats] 10567094

ALTER   PROCEDURE [MTX].[GetCustomerStats]
(
    @CustomerId BIGINT = 0
)
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;
    
    IF (@CustomerId IS NULL OR @CustomerId = 0)
    BEGIN
        RETURN
    END

    IF EXISTS(SELECT TOP 1 1 FROM CRM.InvalidMobileNumbers (NOLOCK) WHERE CustID = @CustomerId)
    BEGIN
        RETURN
    END
    DROP TABLE IF EXISTS #CustStats;
 -- create a temp table with fields of CustomerCallStats
    CREATE TABLE #CustStats (
        [CustomerID] BIGINT ,
        [day_of_week] INT ,
        [day_name] VARCHAR(10) ,
        [hour_of_day] SMALLINT ,
        [answeredCount] INT DEFAULT(0),
        [answeredCount15s] INT DEFAULT(0),
        [answeredCount2min] INT DEFAULT(0),
        [unansweredCount] INT DEFAULT(0),
        [call_count] INT  DEFAULT(0),
        [total_talk_time] BIGINT DEFAULT(0),
        [appointment_visit_count] SMALLINT DEFAULT(0),
        [appointment_count] SMALLINT DEFAULT(0),
        [callback_attempts] SMALLINT DEFAULT(0),
        [callback_success] SMALLINT DEFAULT(0),
        [callback_count] SMALLINT DEFAULT(0)
    );


    INSERT INTO #CustStats (CustomerID, day_of_week, day_name, hour_of_day, answeredCount, answeredCount15s, answeredCount2min, unansweredCount, call_count, total_talk_time, callback_attempts, callback_success)
    SELECT
        ld.CustomerID,
        DATEPART(dw, CallDate) AS day_of_week,
        CASE DATEPART(dw, CallDate)
            WHEN 1 THEN 'SUN' WHEN 2 THEN 'M' WHEN 3 THEN 'TU'
            WHEN 4 THEN 'W' WHEN 5 THEN 'TH' WHEN 6 THEN 'F'
            WHEN 7 THEN 'SAT'
        END AS day_name,
        DATEPART(HOUR, CallDate) AS hour_of_day,
        SUM(CASE WHEN talktime > 0 THEN 1 ELSE 0 END) AS answeredCount,
        SUM(CASE WHEN talktime > 15 THEN 1 ELSE 0 END) AS answeredCount15s,
        SUM(CASE WHEN talktime >= 120 THEN 1 ELSE 0 END) AS answeredCount2min,
        SUM(CASE WHEN talktime = 0 THEN 1 ELSE 0 END) AS unansweredCount,
        COUNT(1) AS call_count,
        SUM(talktime) AS total_talk_time,
        SUM(CASE WHEN (DATEDIFF(MINUTE, CallDate, ED.EventDate) BETWEEN -30 AND 30) THEN 1 ELSE 0 END) AS callback_attempts,
        SUM(CASE WHEN (DATEDIFF(MINUTE, CallDate, ED.EventDate) BETWEEN -30 AND 30 AND talktime > 0) THEN 1 ELSE 0 END) AS callback_success
    FROM MTX.calldatahistory cdh (NOLOCK)
    INNER JOIN Matrix.crm.leaddetails ld (NOLOCK) ON ld.LeadID = cdh.leadid
    LEFT JOIN  CRM.InvalidMobileNumbers INVM WITH(NOLOCK) ON INVM.MobileNo=LD.MobileNo
    LEFT JOIN CRM.EventDetails ED WITH(NOLOCK) ON ED.LEADID = LD.LEADID                  
    WHERE ld.CustomerID = @CustomerID
    AND INVM.id IS NULL
        AND cdh.Duration > 2
        AND cdh.Context <> 'twowaycall'
    GROUP BY ld.CustomerID, DATEPART(dw, CallDate), DATEPART(HOUR, CallDate)
        

    -- Update existing entries with appointment counts
    UPDATE cs
    SET appointment_count = ISNULL(apt.appointment_count, 0)
    FROM #CustStats cs
    INNER JOIN (
        SELECT
            CustomerID,
            DATEPART(dw, AppointmentDateTime) AS day_of_week,
            DATEPART(HOUR, AppointmentDateTime) AS hour_of_day,
            SUM(CASE WHEN ASS.SubstatusId IN (2124,2003) THEN 1 ELSE 0 END) AS appointment_visit_count,
            COUNT(1) AS appointment_count
        FROM MTX.AppointmentData AD (NOLOCK)
        INNER JOIN MTX.AppointmentSubStatus ASS ON AD.Id = ASS.AppointmentID AND ASS.isLastStatus=1
        WHERE
            CustomerID = @CustomerID
            AND AppointmentDateTime IS NOT NULL
        GROUP BY CustomerID, DATEPART(dw, AppointmentDateTime), DATEPART(HOUR, AppointmentDateTime)
    ) apt ON cs.CustomerID = apt.CustomerID
        AND cs.day_of_week = apt.day_of_week
        AND cs.hour_of_day = apt.hour_of_day;

    -- Insert new entries for appointment times that don't exist in call data
    INSERT INTO #CustStats (CustomerID, day_of_week, day_name, hour_of_day, appointment_visit_count ,appointment_count)
    SELECT
        apt.CustomerID,
        apt.day_of_week,
        CASE apt.day_of_week
            WHEN 1 THEN 'SUN' WHEN 2 THEN 'M' WHEN 3 THEN 'TU'
            WHEN 4 THEN 'W' WHEN 5 THEN 'TH' WHEN 6 THEN 'F'
            WHEN 7 THEN 'SAT'
        END AS day_name,
        apt.hour_of_day,
        apt.appointment_visit_count,
        apt.appointment_count
    FROM (
        SELECT
            AD.CustomerID,
            DATEPART(dw, AppointmentDateTime) AS day_of_week,
            DATEPART(HOUR, AppointmentDateTime) AS hour_of_day,
            SUM(CASE WHEN ASS.SubstatusId IN (2124,2003) THEN 1 ELSE 0 END) AS appointment_visit_count,
            COUNT(1) AS appointment_count
        FROM MTX.AppointmentData AD (NOLOCK)
        INNER JOIN MTX.AppointmentSubStatus ASS ON AD.Id = ASS.AppointmentID AND ASS.isLastStatus=1
        WHERE
            AD.CustomerID = @CustomerID
            AND AppointmentDateTime IS NOT NULL
        GROUP BY CustomerID, DATEPART(dw, AppointmentDateTime), DATEPART(HOUR, AppointmentDateTime)
    ) apt
    WHERE NOT EXISTS (
        SELECT 1 FROM #CustStats cs
        WHERE cs.CustomerID = apt.CustomerID
            AND cs.day_of_week = apt.day_of_week
            AND cs.hour_of_day = apt.hour_of_day
    );



    -- update total callback
    UPDATE cs
    SET callback_count = ISNULL(callbackData.callback_count, 0)
    FROM #CustStats cs
    INNER JOIN (
        SELECT
            ed.CustomerID,
            DATEPART(dw, ed.EventDate) AS day_of_week,
            DATEPART(HOUR, ed.EventDate) AS hour_of_day,
            COUNT(1) AS callback_count
        FROM CRM.EventDetails (NOLOCK) ED
        INNER JOIN Matrix.crm.leaddetails ld (NOLOCK) ON ld.LeadID = ED.LeadID
        WHERE ld.CustomerID = @CustomerID
        GROUP BY ED.CustomerID, DATEPART(dw, ed.EventDate), DATEPART(HOUR, ed.EventDate)
    ) callbackData ON cs.CustomerID = callbackData.CustomerID
        AND cs.day_of_week = callbackData.day_of_week
        AND cs.hour_of_day = callbackData.hour_of_day;

    INSERT INTO #CustStats (CustomerID, day_of_week, day_name, hour_of_day, callback_count)
    SELECT
        cb.CustomerID,
        cb.day_of_week,
        CASE cb.day_of_week
            WHEN 1 THEN 'SUN' WHEN 2 THEN 'M' WHEN 3 THEN 'TU'
            WHEN 4 THEN 'W' WHEN 5 THEN 'TH' WHEN 6 THEN 'F'
            WHEN 7 THEN 'SAT'
        END AS day_name,
        cb.hour_of_day,
        cb.callback_count
    FROM (
        SELECT
            ED.CustomerID,
            DATEPART(dw, ED.EventDate) AS day_of_week,
            DATEPART(HOUR, ED.EventDate) AS hour_of_day,
            COUNT(1) AS callback_count
        FROM CRM.EventDetails (NOLOCK) ED
        INNER JOIN Matrix.crm.leaddetails ld (NOLOCK) ON ld.LeadID = ED.LeadID
        WHERE ld.CustomerID = @CustomerID
        GROUP BY ED.CustomerID, DATEPART(dw, ed.EventDate), DATEPART(HOUR, ed.EventDate)
    ) cb
    WHERE NOT EXISTS (
        SELECT 1 FROM #CustStats cs
        WHERE cs.CustomerID = cb.CustomerID
            AND cs.day_of_week = cb.day_of_week
            AND cs.hour_of_day = cb.hour_of_day
    );

    select * from #CustStats;

END


--- RESPONSE TABLE HEADERS
-- CustomerID	day_of_week	day_name	hour_of_day	answeredCount	answeredCount15s	answeredCount2min	unansweredCount	call_count	total_talk_time	appointment_visit_count	appointment_count	callback_attempts	callback_success	callback_count