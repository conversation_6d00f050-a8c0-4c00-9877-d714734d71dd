-- [MTX].[BestTimeToCallParams] 10766493
-- =============================================
-- Author:      <PERSON><PERSON>
-- Create date: 10-06-2025
-- Description: BestTimeToCallParams
-- =============================================

CREATE   PROCEDURE [MTX].[BestTimeToCallParams]
(
    @CustomerId BIGINT=0
)
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;
    IF (@CustomerId IS NULL or @CustomerId=0)
    BEGIN
        RETURN
    END

    IF EXISTS(SELECT top 1 1 FROM CRM.InvalidMobileNumbers where CustID=@CustomerId)
    BEGIN
        RETURN
    END
    -- Basic scoring , weights are choosen by us for answerrate, avg TT etc...

    DECLARE @MinCallAttempts INT = 3; -- Minimum sample size for reliable data

    WITH CustomerCallStats AS (
        SELECT 
            ld.CustomerID,
            DATEPART(dw, CallDate) AS day_of_week,
            CASE DATEPART(dw, CallDate) 
            WHEN 1 THEN 'Sunday' WHEN 2 THEN 'Monday' WHEN 3 THEN 'Tuesday' 
            WHEN 4 THEN 'Wednesday' WHEN 5 THEN 'Thursday' WHEN 6 THEN 'Friday' 
            WHEN 7 THEN 'Saturday' 
            END AS day_name,
            DATEPART(HOUR, CallDate) AS hour_of_day,
            CAST(SUM(CASE WHEN talktime > 15 THEN 1 ELSE 0 END) AS FLOAT) / 
            NULLIF(COUNT(1), 0) AS answer_rate,
            SUM(CASE WHEN talktime > 15 THEN 1 ELSE 0 END) AS answeredCount,
            SUM(CASE WHEN talktime > 15 THEN 0 ELSE 1 END) AS unansweredCount,
            COUNT(1) AS call_count,
            AVG(CASE WHEN talktime > 0 THEN talktime ELSE NULL END) AS avg_talk_time,
            SUM(talktime) AS SUM_talk_time
        FROM MTX.calldatahistory cdh
        INNER JOIN Matrix.crm.leaddetails ld ON ld.LeadID = cdh.leadid
        WHERE ld.CustomerID = @CustomerID
        AND cdh.Duration>2 AND cdh.Context<>'twowaycall'
        GROUP BY ld.CustomerID, DATEPART(dw, CallDate), DATEPART(HOUR, CallDate)
    ),


    TimeSlotScoring AS (
        SELECT 
            CustomerID,
            day_of_week,
            day_name,
            hour_of_day,
            answer_rate,
            answeredCount,
            unansweredCount,
            call_count,
            avg_talk_time,
            -- Create a composite score based on multiple factors
            (answer_rate * 50) + 
            (answeredCount * 2) + 
            (CASE WHEN call_count >= @MinCallAttempts THEN 10 ELSE 0 END) +
            (CASE WHEN avg_talk_time > 60 THEN 10 ELSE 0 END) AS call_score
        FROM CustomerCallStats
        WHERE call_count >= @MinCallAttempts
    )
    SELECT CustomerID,
    day_name,
    hour_of_day, answer_rate_pct,total_attempts, answeredCount,unansweredCount,avg_talk_seconds, call_score, recommendation, rn FROM (
        SELECT 
        CustomerID,
        day_name,
        hour_of_day,
        CAST(answer_rate * 100 AS DECIMAL(5,1)) AS answer_rate_pct,
        call_count AS total_attempts,
        answeredCount,
        unansweredCount,
        ISNULL(CAST(avg_talk_time AS INT), 0) AS avg_talk_seconds,
        call_score,
        CASE 
            WHEN call_score >= 50 THEN 'Highly Recommended'
            WHEN call_score >= 30 THEN 'Recommended'
            WHEN call_score >= 15 THEN 'Consider'
            ELSE 'Not Recommended'
        END AS recommendation,
        ROW_NUMBER() OVER (PARTITION BY day_name ORDER BY call_score desc) as rn
        FROM TimeSlotScoring
        -- ORDER BY call_score DESC 
    ) A
    WHERE rn <= 2
    ORDER BY call_score DESC 
    
END
