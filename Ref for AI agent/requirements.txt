SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- EXEC [MTX].[GetDemographicCallData] @CustomerID=9358
--[MTX].[GetDemographicCallData] 9358
ALTER PROCEDURE [MTX].[GetDemographicCallData]
(@CustomerID BIGINT=0
)
AS 
BEGIN
DROP TABLE IF EXISTS #LeadData,#Calldata,#tempData;
WITH GenderMap AS (
    SELECT *
    FROM (VALUES
        ('1', 1), ('4', 1), ('Male', 1), ('Mr', 1), ('Mr.', 1), ('mr.', 1), ('M', 1),
        ('2', 2), ('Mrs.', 2), ('M/S', 2), ('Ms', 2), ('MS', 2), ('Ms.', 2), ('Female', 2), ('F', 2)
    ) AS GMap(GenderLabel, GenderCode)
)
select LeadID AS ParentId, CustomerID,ProductID,DOB,CreatedON,ISNULL(<PERSON><PERSON>ode,0) AS Gender,UpdatedON,cityId,AnnualIncome
INTO #LeadData
from matrix.crm.leaddetails LDD WITH (NOLOCK)
LEFT JOIN GenderMap G ON LTRIM(RTRIM(LDD.Gender)) = G.GenderLabel
WHERE CustomerID=@CustomerID and ParentID is null
order by CreatedON desc


select @CustomerID AS CustomerId,DATENAME(WEEKDAY, C.CallDate) AS DayName,
        DATEPART(HOUR, C.CallDate) AS CallHour, sum(talktime) AS talktime,
        COUNT(1) TotalCalls, 
        SUM(CASE WHEN talktime > 15 THEN 1 ELSE 0 END) AnsweredCalls,
        SUM(CASE WHEN talktime >= 120 THEN 1 ELSE 0 END) [2MinAnsweredCalls]
INTO #Calldata
from mtx.CallDataHistory c
INNER JOIN #LeadData LD ON C.LeadID=LD.ParentId AND Duration > 2
GROUP BY DATENAME(WEEKDAY, C.CallDate) ,
  DATEPART(HOUR, C.CallDate) 
  order by DATENAME(WEEKDAY, C.CallDate) ,
  DATEPART(HOUR, C.CallDate) 

;WITH BrandMap AS (
    SELECT *
    FROM (VALUES
        ('Apple', 'Flagship Brands'),
        ('OnePlus', 'Flagship Brands'),
        ('HUAWEI', 'Flagship Brands'),
        ('Google', 'Flagship Brands'),
        ('Asus', 'Flagship Brands'),
        ('LGE', 'Flagship Brands'),
        ('Samsung', 'Flagship Brands'),

        ('Sony', 'Affordable_brands'),
        ('Lenovo', 'Affordable_brands'),
        ('Realme', 'Affordable_brands'),
        ('motorola', 'Affordable_brands'),
        ('Nothing', 'Affordable_brands'),
        ('Xiaomi', 'Affordable_brands'),
        ('Vivo', 'Affordable_brands'),
        ('Oppo', 'Affordable_brands'),
        ('Infinix', 'Affordable_brands'),
        ('Generic Android', 'Affordable_brands'),
        ('Mozilla', 'Affordable_brands'),
        ('INFINIX MOBILITY LIMITED', 'Affordable_brands'),
        ('HONOR', 'Affordable_brands'),
        ('LAVA', 'Affordable_brands'),
        ('Micromax', 'Affordable_brands'),
        ('TECNO', 'Affordable_brands')
    ) AS B(brandname, BrandCategory)
)
select LD.CustomerID,max(LD.CreatedOn) as createdon,max(CityID) as cityId,max(DOB) as DOB ,max(Gender) as Gender,max(AnnualIncome) AS Income,max(ProfessionType)as Professiontype, ISNULL(max(BM.BrandCategory), '03. Rest') AS Brandname, max(educationQualificationId) AS Education
INTO #tempData
FROM #LeadData LD
LEFT JOIN mtx.termdetails TD WITH (NOLOCK) ON LD.ParentId=TD.LeaDId and LD.ProductID=7
LEFT JOIN  BrandMap BM ON TD.brandname = BM.brandname
GROUP BY LD.CustomerID
--- CallData will have parentLeadId

select TD.CustomerID,CD.DayName,CD.CallHour,CD.talktime,createdon,CityId, DOB,Gender,Income,Professiontype,Brandname,CD.TotalCalls,CD.AnsweredCalls,CD.[2MinAnsweredCalls]
FROM #tempData TD
INNER JOIN #Calldata CD ON TD.CustomerID=CD.CustomerId
order by DayName,CallHour
END
GO
