-- =============================================
-- Author: <PERSON><PERSON>
-- Create date: 04-07-2025
-- Description: Optimized BestTimeToCallParams using pre-calculated CustomerCallStats table
-- =============================================

CREATE   PROCEDURE [MTX].[BestTimeToCallParams_Optimized]
(
    @CustomerId BIGINT = 0,
    @RefreshData BIT = 0  -- If 1, refreshes data for this customer before analysis
)
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;
    
    IF (@CustomerId IS NULL OR @CustomerId = 0)
    BEGIN
        RETURN
    END

    IF EXISTS(SELECT TOP 1 1 FROM CRM.InvalidMobileNumbers (NOLOCK) WHERE CustID = @CustomerId)
    BEGIN
        RETURN
    END

    -- Optionally refresh data for this customer if requested
    IF @RefreshData = 1
    BEGIN
        EXEC [MTX].[PopulateCustomerCallStats] @CustomerID = @CustomerId;
    END

    -- Check if we have data for this customer
    IF NOT EXISTS(SELECT 1 FROM [MTX].[CustomerCallStats] (NOLOCK) WHERE CustomerID = @CustomerId)
    BEGIN
        -- If no pre-calculated data exists, populate it for this customer
        EXEC [MTX].[PopulateCustomerCallStats] @CustomerID = @CustomerId;
    END

    DECLARE @MinCallAttempts INT = 3; -- Minimum sample size for reliable data

    WITH TimeSlotScoring AS (
        SELECT
            CustomerID,
            day_of_week,
            day_name,
            hour_of_day,
            -- Calculate derived values from raw data
            CAST(answeredCount15s AS FLOAT) / NULLIF(call_count, 0) AS answer_rate,
            answeredCount,
            answeredCount15s,
            unansweredCount,
            call_count,
            CASE WHEN answeredCount > 0 THEN CAST(total_talk_time AS FLOAT) / answeredCount ELSE 0 END AS avg_talk_time,
            -- Create a composite score based on multiple factors (using 15s+ answered calls for scoring)
            (CAST(answeredCount15s AS FLOAT) / NULLIF(call_count, 0) * 50) +
            (answeredCount15s * 2) +
            (CASE WHEN call_count >= @MinCallAttempts THEN 10 ELSE 0 END) +
            (CASE WHEN (CASE WHEN answeredCount > 0 THEN CAST(total_talk_time AS FLOAT) / answeredCount ELSE 0 END) > 60 THEN 10 ELSE 0 END) AS call_score
        FROM [MTX].[CustomerCallStats] (NOLOCK)
        WHERE CustomerID = @CustomerId
            AND call_count >= @MinCallAttempts
    )
    SELECT
        CustomerID,
        day_name,
        hour_of_day,
        answer_rate_pct,
        total_attempts,
        answeredCount,
        answeredCount15s,
        unansweredCount,
        avg_talk_seconds,
        call_score,
        recommendation,
        rn
    FROM (
        SELECT
            CustomerID,
            day_name,
            hour_of_day,
            CAST(answer_rate * 100 AS DECIMAL(5,1)) AS answer_rate_pct,
            call_count AS total_attempts,
            answeredCount,
            answeredCount15s,
            unansweredCount,
            ISNULL(CAST(avg_talk_time AS INT), 0) AS avg_talk_seconds,
            call_score,
            CASE
                WHEN call_score >= 50 THEN 'Highly Recommended'
                WHEN call_score >= 30 THEN 'Recommended'
                WHEN call_score >= 15 THEN 'Consider'
                ELSE 'Not Recommended'
            END AS recommendation,
            ROW_NUMBER() OVER (PARTITION BY day_name ORDER BY call_score DESC) AS rn
        FROM TimeSlotScoring
    ) A
    WHERE rn <= 2
    ORDER BY call_score DESC;
    
END
