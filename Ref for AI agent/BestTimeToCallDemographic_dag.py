import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))
from ConnectionConfigs.ConnectionString import ConnectionString
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from airflow.models import Variable
import pyodbc
import pendulum
from airflow.providers.mongo.hooks.mongo import MongoHook

default_args = {
    'owner': '<PERSON><PERSON><PERSON>',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 1, 1, tz="Asia/Kolkata"),
    'retries': 1,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'BestTimeToCallDemographic_dag',
    default_args=default_args,
    schedule_interval='0 2 * * *',  # Daily at 2 AM
    catchup=False,
    max_active_runs=1,
    tags=["Matrix", "CustomerData"]
)
def test_with_manual_customer_id():
    """
    Test function to manually test with a specific customer ID
    You can modify this to test with a known customer ID that should have data
    """
    try:
        # Replace with a customer ID that you know should have data
        test_customer_id = "768"  # Change this to a known customer ID
        
        print(f"Manual test with customer ID: {test_customer_id}")
        
        # Run comprehensive diagnosis first
        print("\n" + "=" * 60)
        print("RUNNING COMPREHENSIVE DIAGNOSIS")
        print("=" * 60)
        
        success = comprehensive_diagnosis()
        
        if not success:
            print("\n✗ DIAGNOSIS FAILED - Please check the errors above")
            return
        
        print(f"\n" + "=" * 60)
        print(f"TESTING SPECIFIC CUSTOMER ID: {test_customer_id}")
        print("=" * 60)
        
        # Test the stored procedure parameters first
        print("\n=== CHECKING STORED PROCEDURE PARAMETERS ===")
        check_stored_procedure_parameters()
        
        # Test parameter types directly
        print("\n=== TESTING PARAMETER TYPES DIRECTLY ===")
        successful_types = test_parameter_types_directly(test_customer_id)
        
        # Test the stored procedure directly
        print("\n=== TESTING STORED PROCEDURE DIRECTLY ===")
        test_stored_procedure_directly(test_customer_id)
        
        # Test with the actual fetch function
        print("\n=== TESTING WITH FETCH FUNCTION ===")
        result = fetch_customer_data(test_customer_id)
        
        if result:
            print(f"SUCCESS: Manual test found data for customer {test_customer_id}")
            print(f"Number of rows: {len(result['data'])}")
            print(f"Columns: {result['columns']}")
        else:
            print(f"FAILED: Manual test found no data for customer {test_customer_id}")
            
    except Exception as e:
        print(f"Error in manual test: {e}")

# Database connection
connection_string_matrix =ConnectionString(30, "Matrix_Primary")

def get_customer_ids():
    """
    Fetch all customer IDs from stored procedure
    """
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
                
        # Replace with your actual stored procedure name
        procedure_name = '[MTX].[FetchAllCustomer]'
        print(f"Executing: {procedure_name}")
        cursor.execute(f"EXEC {procedure_name}")
        
        customer_ids = cursor.fetchall()
        print(f"Fetched {len(customer_ids)} customer IDs")       
        
        cursor.close()
        connection.close()
        
        # Process each customer ID
        process_customer_data(customer_ids)
        
    except pyodbc.Error as e:
        print(f"Database Error in get_customer_ids: {e}")
    except Exception as e:
        print(f"Exception in get_customer_ids: {e}")

def process_customer_data(customer_ids):
    """
    Process each customer ID by calling stored procedure and dumping to MongoDB
    """
    processed_count = 0
    
    # Debug: Show first few customer IDs
    print(f"Total customer IDs received: {len(customer_ids)}")
    if customer_ids:
        print(f"First 5 customer IDs: {[row[0] if row else None for row in customer_ids[:5]]}")

    for customer_row in customer_ids[:20]:
        if not customer_row or not customer_row[0]:
            print(f"Skipping invalid customer row: {customer_row}")
            continue
            
        customer_id = customer_row[0]
        print(f"Processing customer ID: {customer_id}")
        
        try:
            # Fetch data for this customer
            customer_data = fetch_customer_data(customer_id)
            
            if customer_data:
                # Dump to MongoDB
                dump_to_mongo(customer_data, customer_id)
                processed_count += 1
            else:
                print(f"No data returned for customer {customer_id}")
                
        except Exception as e:
            print(f"Error processing customer {customer_id}: {e}")
            continue
    
    print(f"Successfully processed {processed_count} customers out of {min(20, len(customer_ids))} attempted")

def fetch_customer_data(customer_id):
    """
    Fetch data for a specific customer from stored procedure
    """
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        # Add debugging information
        print(f"Calling stored procedure for customer ID: {customer_id} (type: {type(customer_id)})")
        
        variation = customer_id
        # Try each variation
        try:
            cursor.execute(f"EXEC [MTX].[GetDemographicCallData] @CustomerID = 768")
            customer_data = cursor.fetchall()
            print(f"Fetched {len(customer_data)} rows for customer 768111")

            # Run the SP safely
            cursor.execute(f"EXEC [MTX].[GetDemographicCallData] @CustomerID = ?", (variation,))
            
            # Check if the result is a SELECT query
            if cursor.description is None:
                print(f"No result set returned for customer {variation} — not a SELECT query.")
            
            # Fetch all data for this customer
            customer_data = cursor.fetchall()
            print(f"Fetched {len(customer_data)} rows for customer {variation}")
            
            # If we got data, use this variation
            if customer_data:
                print(f"SUCCESS: Found data using customer ID: {variation} (type: {type(variation)})")
                print("First row data:")
                print(customer_data[0])
                
                # Get column names
                column_names = [desc[0] for desc in cursor.description]
                print(f"Column names: {column_names}")
                
                cursor.close()
                connection.close()
                
                return {
                    'data': customer_data,
                    'columns': column_names
                }
            else:
                print(f"No data rows returned for customer {variation}")
                
        except pyodbc.Error as e:
            print(f"Database Error with variation {variation}: {e}")
            # continue
        except Exception as e:
            print(f"Exception with variation {variation}: {e}")
            # continue
        
        # If we get here, none of the variations worked
        print(f"No variations worked for customer {customer_id}")
        
        cursor.close()
        connection.close()
        return None
        
    except pyodbc.Error as e:
        print(f"Database Error in fetch_customer_data for customer {customer_id}: {e}")
        return None
    except Exception as e:
        print(f"Exception in fetch_customer_data for customer {customer_id}: {e}")
        return None

def dump_to_mongo(customer_data, customer_id):
    """
    Dump customer data to MongoDB
    """
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['BestTimeToCallDemographic']
      
        
        # Convert data to MongoDB documents
        documents = []
        data_rows = customer_data['data']
        columns = customer_data['columns']
        
        for row in data_rows:
            document = {
                'CustomerId': customer_id,
                'ProcessedOn': datetime.now(),
                'CreatedBy': 'Airflow_DAG'
            }
            
            # Map column names to values
            for i, column in enumerate(columns):
                if i < len(row):
                    document[column] = row[i]
            
            documents.append(document)
        
        if documents:
            # Insert documents into MongoDB
            result = collection.insert_many(documents)
            print(f"Inserted {len(result.inserted_ids)} documents for customer {customer_id}")
        else:
            print(f"No data to insert for customer {customer_id}")
            
    except Exception as e:
        print(f"Exception in dump_to_mongo for customer {customer_id}: {e}")

# Define the tasks
test_manual_customer_task = PythonOperator(
    task_id='test_manual_customer',
    python_callable=test_with_manual_customer_id,
    provide_context=True,
    dag=dag,
)

customer_data_processing_task = PythonOperator(
    task_id='process_customer_data',
    python_callable=get_customer_ids,
    provide_context=True,
    dag=dag,
)

# Enable the test task to run first, then the main processing
# test_manual_customer_task >> customer_data_processing_task
customer_data_processing_task
# If you want to run only the test task, comment out the line above and uncomment below:
# test_manual_customer_task 