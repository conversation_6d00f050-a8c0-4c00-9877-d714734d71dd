SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- [MTX].[GetRecentCalledCustomers]
-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 10-06-2025
-- Description:	GetRecentCalledCustomers
-- =============================================

CREATE OR ALTER PROCEDURE [MTX].[GetRecentCalledCustomers]
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;

    SELECT DISTINCT ld.CustomerID
    FROM MTX.calldatahistory cdh
    INNER JOIN Matrix.crm.leaddetails ld ON ld.LeadID = cdh.leadid
    WHERE cdh.UpdatedOn > DATEADD(MINUTE,-70,GETDATE())
    AND cdh.CallDate > DATEADD(HOUR,-4,GETDATE())
    AND Duration > 2 
    AND CDH.Context <> 'twowaycall';
END
GO
