-- =============================================
-- Author: <PERSON><PERSON>
-- Create date: 04-07-2025
-- Description: Populates CustomerCallStats table with aggregated call data
-- =============================================

CREATE   PROCEDURE [MTX].[PopulateCustomerCallStats]
(
    @CustomerID BIGINT = 0 -- Specific customer to process
)
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;

    IF (@CustomerID IS NULL OR @CustomerID = 0)
    BEGIN
        RETURN
    END

    DECLARE @ProcessedCount INT = 0;

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Delete existing data for this customer to refresh it
        DELETE FROM [MTX].[CustomerCallStats] WHERE CustomerID = @CustomerID;

        -- Generate the raw aggregated call statistics
        WITH CustomerCallStats AS (
            SELECT
                ld.CustomerID,
                DATEPART(dw, CallDate) AS day_of_week,
                CASE DATEPART(dw, CallDate)
                    WHEN 1 THEN 'Sunday' WHEN 2 THEN 'Monday' WHEN 3 THEN 'Tuesday'
                    WHEN 4 THEN 'Wednesday' WHEN 5 THEN 'Thursday' WHEN 6 THEN 'Friday'
                    WHEN 7 THEN 'Saturday'
                END AS day_name,
                DATEPART(HOUR, CallDate) AS hour_of_day,
                SUM(CASE WHEN talktime > 0 THEN 1 ELSE 0 END) AS answeredCount,
                SUM(CASE WHEN talktime > 15 THEN 1 ELSE 0 END) AS answeredCount15s,
                SUM(CASE WHEN talktime = 0 THEN 1 ELSE 0 END) AS unansweredCount,
                COUNT(1) AS call_count,
                SUM(talktime) AS total_talk_time
            FROM MTX.calldatahistory cdh (NOLOCK)
            INNER JOIN Matrix.crm.leaddetails ld (NOLOCK) ON ld.LeadID = cdh.leadid
            WHERE ld.CustomerID = @CustomerID
                AND cdh.Duration > 2
                AND cdh.Context <> 'twowaycall'
                -- Only process customers not in invalid mobile numbers
                AND NOT EXISTS(SELECT 1 FROM CRM.InvalidMobileNumbers (NOLOCK) WHERE CustID = ld.CustomerID)
            GROUP BY ld.CustomerID, DATEPART(dw, CallDate), DATEPART(HOUR, CallDate)
        )
        -- Insert the raw statistics (since we deleted existing data above)
        INSERT INTO [MTX].[CustomerCallStats]
        (CustomerID, day_of_week, day_name, hour_of_day, answeredCount, answeredCount15s, unansweredCount, call_count, total_talk_time)
        SELECT
            CustomerID,
            day_of_week,
            day_name,
            hour_of_day,
            answeredCount,
            answeredCount15s,
            unansweredCount,
            call_count,
            total_talk_time
        FROM CustomerCallStats;

        SET @ProcessedCount = @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        -- PRINT 'Successfully processed ' + CAST(@ProcessedCount AS VARCHAR(10)) + ' customer call stat records.';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
