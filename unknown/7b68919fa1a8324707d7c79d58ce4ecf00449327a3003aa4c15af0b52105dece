from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.models import Variable
from datetime import datetime, timedelta
import pendulum
import pyodbc
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson import ObjectId

# Connection strings and configurations
from ConnectionConfigs.ConnectionString import ConnectionString
connection_string_matrix = ConnectionString(30, "Matrix_Primary")

# Default arguments for the DAG
default_args = {
    'owner': 'Matrix',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2025, 7, 15, tz="Asia/Kolkata"),
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
}

# Create the DAG
dag = DAG(
    'CustomerStatsDAG',
    default_args=default_args,
    description='Process customer statistics from BestTimeToCall collection and store in CustomerStats',
    schedule_interval=timedelta(hours=1),  # Run hourly
    catchup=False,
    max_active_runs=1,
)

def get_customer_ids_batch():
    """Get a batch of CustomerIDs from BestTimeToCall collection using ObjectId marker"""
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['BestTimeToCall']
        
        # Get configuration from Airflow Variables
        batch_size = int(Variable.get("CustomerStats_BatchSize", default_var="25000"))
        
        # Get the last processed ObjectId to continue from where we left off
        last_processed_id = Variable.get("CustomerStats_LastProcessedObjectId", default_var=None)
        
        # Build query based on last processed ObjectId
        query = {}
        if last_processed_id:
            try:
                query = {"_id": {"$gt": ObjectId(last_processed_id)}}
            except Exception as e:
                print(f"Invalid ObjectId in variable, starting from beginning: {e}")
                query = {}
        
        # Get batch of documents
        cursor = collection.find(query).sort("_id", 1).limit(batch_size)
        documents = list(cursor)
        
        if not documents:
            print("No more documents to process")
            return [], None
        
        # Extract CustomerIDs and get last ObjectId
        customer_ids = [doc["CustomerID"] for doc in documents if "CustomerID" in doc]
        last_object_id = str(documents[-1]["_id"])
        
        print(f"Retrieved {len(customer_ids)} customer IDs for processing")
        print(f"Last ObjectId: {last_object_id}")
        
        return customer_ids, last_object_id
        
    except Exception as e:
        print(f"Error in get_customer_ids_batch: {str(e)}")
        return [], None

def get_customer_stats_from_sql(customer_id):
    """Call GetCustomerStats stored procedure for a single customer"""
    try:
        # Connect to SQL Server
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()

        # Execute the stored procedure
        cursor.execute(f"EXEC [MTX].[GetCustomerStats] @CustomerId = {customer_id}")

        # Check if there are results to fetch
        try:
            rows = cursor.fetchall()
            has_results = True
        except pyodbc.ProgrammingError as pe:
            if "No results. Previous SQL was not a query." in str(pe):
                print(f"No results returned for customer ID {customer_id}")
                has_results = False
                rows = []
            else:
                # Re-raise if it's a different error
                raise

        # Close connections
        cursor.close()
        connection.close()

        if not has_results or not rows:
            print(f"No stats data found for customer {customer_id}")
            return []

        return rows

    except Exception as e:
        print(f"Error getting stats for customer {customer_id}: {str(e)}")
        return []

def format_customer_stats_data(customer_id, sql_rows):
    """Format SQL results into the required MongoDB document structure"""
    try:
        if not sql_rows:
            return None

        # Initialize the document structure
        customer_doc = {
            "cid": customer_id,
            "d": []
        }
        
        # Process each row from SQL results
        for row in sql_rows:
            day_data = {
                "dw": row.day_of_week,           # day_of_week
                "dn": row.day_name,             # day_name
                "h": row.hour_of_day,           # hour_of_day
                "ans": row.answeredCount,       # answered
                "ans15": row.answeredCount15s,  # answered within 15 sec
                "ans2": row.answeredCount2min,  # answered within 2 min
                "na": row.unansweredCount,      # not answered
                "tc": row.call_count,           # total calls
                "tt": row.total_talk_time,      # total talk time (seconds)
                "apptV": row.appointment_visit_count,  # appointment visits
                "appt": row.appointment_count,  # total appointments
                "cb": row.callback_attempts,    # callback attempts
                "cbS": row.callback_success,    # callback success
                "cbC": row.callback_count       # callback count
            }
            customer_doc["d"].append(day_data)
        
        return customer_doc
        
    except Exception as e:
        print(f"Error formatting data for customer {customer_id}: {str(e)}")
        return None


def store_customer_stats_in_mongo(customer_docs):
    """Store customer statistics documents in MongoDB CustomerStats collection"""
    try:
        if not customer_docs:
            return 0
        
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['CustomerStats']
        
        # Use upsert to replace existing documents based on customer ID
        operations = []
        # for doc in customer_docs:
        #     operations.append({
        #         "replaceOne": {
        #             "filter": {"cid": doc["cid"]},
        #             "replacement": doc,
        #             "upsert": True
        #         }
        #     })
        
        # Execute bulk operations
        # if operations:
        #     result = collection.bulk_write(operations)
        #     print(f"Stored {result.upserted_count + result.modified_count} customer stats documents")
        #     return result.upserted_count + result.modified_count
        
        return 0
        
    except Exception as e:
        print(f"Error storing customer stats in MongoDB: {str(e)}")
        return 0

def check_existing_customer_stats(customer_id):
    """Check if customer stats already exist in MongoDB"""
    try:
        # Connect to MongoDB
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        collection = db['CustomerStats']

        # Check if document exists based on customer ID
        existing_doc = collection.find_one({"cid": customer_id})
        return existing_doc is not None
        
    except Exception as e:
        print(f"Error checking existing stats for customer {customer_id}: {str(e)}")
        return False

def process_customer_stats_batch():
    """Main function to process a batch of customer statistics"""
    try:
        print(f"=== Starting CustomerStats batch processing at {datetime.now()} ===")
        hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
        client = hook.get_conn()
        db = client.oneLeadDB
        CustStatscollection = db['CustomerStats']
        lp_collection = db['LPData']

        # Get batch of customer IDs
        customer_ids, last_object_id = get_customer_ids_batch()
        
        if not customer_ids:
            print("No customer IDs to process")
            return
        
        processed_count = 0
        skipped_count = 0
        error_count = 0
        customer_docs = []
        
        # Process each customer ID
        for customer_id in customer_ids:
            try:
                # Validate customer_id
                if not customer_id or customer_id <= 0:
                    print(f"Invalid customer ID: {customer_id}")
                    error_count += 1
                    continue

                # Check if data already exists (optional - remove if you want to always refresh)
                if check_existing_customer_stats(customer_id):
                    # print(f"Skipping customer {customer_id} - data already exists")
                    skipped_count += 1
                    continue

                # Get customer stats from SQL
                sql_rows = get_customer_stats_from_sql(customer_id)

                if sql_rows:
                    customer_doc = format_customer_stats_data(customer_id, sql_rows)

                    if customer_doc:
                        CustStatscollection.update_one(
                            {"cid": customer_id},
                            {"$set": customer_doc},
                            upsert=True
                        )
                        # create a function that creates a score based on parameters, that will decided whether to call or not in that hour of day
                        processed_count += 1
                    else:
                        print(f"Failed to format data for customer {customer_id}")
                        error_count += 1
                else:
                    # print(f"No stats data found for customer {customer_id}")
                    skipped_count += 1

                    
            except Exception as e:
                print(f"Error processing customer {customer_id}: {str(e)}")
                error_count += 1
        
        # Store remaining documents
        if customer_docs:
            store_customer_stats_in_mongo(customer_docs)
        
        # Update the last processed ObjectId in Airflow Variable
        if last_object_id:
            try:
                Variable.set("CustomerStats_LastProcessedObjectId", last_object_id)
                print(f"Updated CustomerStats_LastProcessedObjectId to: {last_object_id}")
            except Exception as e:
                print(f"Error updating CustomerStats_LastProcessedObjectId variable: {e}")
        
        print(f"=== Batch processing completed ===")
        print(f"Processed: {processed_count}, Skipped: {skipped_count}, Errors: {error_count}")
        
    except Exception as e:
        print(f"Error in process_customer_stats_batch: {str(e)}")

# def test_stored_procedure(customer_id=10567094):
#     """Test the GetCustomerStats stored procedure with a sample customer ID"""
#     try:
#         print(f"=== Testing GetCustomerStats stored procedure with customer ID: {customer_id} ===")

#         # Connect to SQL Server
#         connection = pyodbc.connect(connection_string_matrix)
#         cursor = connection.cursor()

#         # Get column information
#         try:
#             print("Checking stored procedure metadata...")
#             cursor.execute(f"SELECT OBJECT_ID('[MTX].[GetCustomerStats]') AS ProcID")
#             proc_id = cursor.fetchone()
#             if not proc_id or not proc_id[0]:
#                 print("ERROR: Stored procedure [MTX].[GetCustomerStats] not found!")
#             else:
#                 print(f"Stored procedure exists with ID: {proc_id[0]}")

#             # Check parameters
#             cursor.execute(f"""
#                 SELECT p.name, t.name AS type_name, p.max_length, p.is_nullable
#                 FROM sys.parameters p
#                 INNER JOIN sys.types t ON p.system_type_id = t.system_type_id
#                 WHERE object_id = OBJECT_ID('[MTX].[GetCustomerStats]')
#             """)
#             params = cursor.fetchall()
#             print("Parameters:")
#             for param in params:
#                 print(f"  - {param.name}: {param.type_name} (nullable: {param.is_nullable})")
#         except Exception as e:
#             print(f"Error checking stored procedure metadata: {e}")

#         # Execute the stored procedure with explicit parameter
#         print(f"Executing stored procedure for customer ID {customer_id}...")
#         cursor.execute(f"EXEC [MTX].[GetCustomerStats] @CustomerId = ?", customer_id)

#         # Try to fetch results
#         try:
#             rows = cursor.fetchall()
#             print(f"Successfully retrieved {len(rows)} rows")

#             # Print column names
#             if rows and len(rows) > 0:
#                 print("Column names:")
#                 for column in cursor.description:
#                     print(f"  - {column[0]}")

#                 # Print first row as sample
#                 print("Sample data (first row):")
#                 first_row = rows[0]
#                 for i, column in enumerate(cursor.description):
#                     print(f"  - {column[0]}: {first_row[i]}")

#             return rows
#         except pyodbc.ProgrammingError as pe:
#             if "No results. Previous SQL was not a query." in str(pe):
#                 print("WARNING: Stored procedure executed but returned no results")
#                 print("This could be normal if the procedure doesn't have a SELECT statement")
#                 print("or if the customer ID doesn't have any data")
#             else:
#                 print(f"ERROR: {str(pe)}")
#             return []
#         finally:
#             cursor.close()
#             connection.close()

#     except Exception as e:
#         print(f"ERROR testing stored procedure: {str(e)}")
#         return []

# def check_collections_status():
#     """Check the status of collections for monitoring"""
#     try:
#         # Connect to MongoDB
#         hook = MongoHook(mongo_conn_id='mongo_onelead', conn_id='mongo_onelead')
#         client = hook.get_conn()
#         db = client.oneLeadDB

#         # Collections
#         best_time_collection = db['BestTimeToCall']
#         customer_stats_collection = db['CustomerStats']

#         # Get collection statistics
#         best_time_count = best_time_collection.count_documents({})
#         customer_stats_count = customer_stats_collection.count_documents({})

#         # Get progress information
#         last_processed_id = Variable.get("CustomerStats_LastProcessedObjectId", default_var=None)
#         batch_size = int(Variable.get("CustomerStats_BatchSize", default_var="25000"))

#         print(f"=== CustomerStats Collection Status ===")
#         print(f"BestTimeToCall collection count: {best_time_count}")
#         print(f"CustomerStats collection count: {customer_stats_count}")
#         print(f"Last processed ObjectId: {last_processed_id}")
#         print(f"Batch size: {batch_size}")

#         # Calculate approximate progress
#         if last_processed_id and best_time_count > 0:
#             # This is an approximation since ObjectId is not strictly sequential
#             progress_percentage = (customer_stats_count / best_time_count) * 100
#             print(f"Approximate progress: {progress_percentage:.2f}%")

#     except Exception as e:
#         print(f"Error in check_collections_status: {str(e)}")

def reset_processing_marker():
    """Reset the processing marker to start from beginning (manual trigger)"""
    try:
        Variable.delete("CustomerStats_LastProcessedObjectId")
        print("Reset CustomerStats_LastProcessedObjectId - will start from beginning next run")
    except Exception as e:
        print(f"Error resetting processing marker: {str(e)}")

# Define the tasks
customer_stats_task = PythonOperator(
    task_id='customer_stats_task',
    python_callable=process_customer_stats_batch,
    dag=dag,
)

# status_check_task = PythonOperator(
#     task_id='status_check_task',
#     python_callable=check_collections_status,
#     dag=dag,
# )

# # Add a test task for the stored procedure
# test_procedure_task = PythonOperator(
#     task_id='test_procedure_task',
#     python_callable=test_stored_procedure,
#     op_kwargs={'customer_id': 10567094},  # Sample customer ID from your example
#     dag=dag,
# )

# Set task dependencies
# status_check_task >> test_procedure_task >> customer_stats_task

customer_stats_task