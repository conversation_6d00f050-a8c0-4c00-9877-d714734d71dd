FROM apache/airflow:2.7.1
COPY requirements.txt /requirements.txt


USER 0

RUN apt-key adv --keyserver keyserver.ubuntu.com --recv-keys A8D3785C

# install FreeTDS and dependencies
RUN apt-get update && apt-get install -y build-essential gettext libcurl4-openssl-dev libssl-dev python-dev curl iputils-ping gnupg2 poppler-utils libpq-dev python3-dev \
 freetds-dev freetds-bin


RUN apt-get install -y python3-dev gcc libc-dev libffi-dev g++

#SQL Driveres
RUN apt-get install -y unixodbc unixodbc-dev
RUN apt install -y odbcinst
RUN apt-get update && \
    apt-get install -y curl apt-transport-https && \
    curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    echo "deb [arch=amd64] https://packages.microsoft.com/debian/10/prod buster main" | tee /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql17

USER airflow

RUN pip install --user --upgrade pip
# RUN pip install --no-cache-dir --user -r /requirements.txt
# RUN pip install pymssql==2.2.5
RUN pip install --user -r /requirements.txt
RUN pip install --upgrade cython

# COPY dags/ ./dags/

