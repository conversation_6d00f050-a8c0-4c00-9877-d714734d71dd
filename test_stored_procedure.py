import sys
from os.path import abspath, dirname
sys.path.append(dirname(dirname(abspath(__file__))))

import pyodbc
from datetime import datetime

# Import your connection string
from dags.ConnectionConfigs.ConnectionString import ConnectionString

def test_stored_procedure_standalone():
    """
    Standalone test script to debug stored procedure issues outside of Airflow
    """
    print("=" * 80)
    print("STANDALONE STORED PROCEDURE TEST")
    print("=" * 80)
    
    # Database connection
    try:
        connection_string_matrix = ConnectionString(30, "Matrix_Primary")
        print("Connection string created successfully")
    except Exception as e:
        print("Failed to create connection string: {}".format(e))
        return
    
    # Test 1: Basic connectivity
    print("\n[TEST 1] Basic Database Connectivity")
    print("-" * 40)
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        cursor.execute("SELECT 1 as test, DB_NAME() as database_name, GETDATE() as current_datetime")
        result = cursor.fetchone()
        print("SUCCESS: Connected to database '{}' at {}".format(result[1], result[2]))
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print("FAILED: Database connection error - {}".format(e))
        return
    
    # Test 2: Check MTX schema and procedures
    print("\n[TEST 2] Schema and Procedures Check")
    print("-" * 40)
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        # Check MTX schema
        cursor.execute("SELECT 1 FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'MTX'")
        if cursor.fetchone():
            print("MTX schema exists")
        else:
            print("MTX schema does not exist")
            return
        
        # Check procedures
        procedures = ['FetchAllCustomer', 'GetDemographicCallData']
        for proc in procedures:
            cursor.execute("SELECT 1 FROM sys.procedures WHERE name = '{}' AND schema_id = SCHEMA_ID('MTX')".format(proc))
            if cursor.fetchone():
                print("[MTX].[{}] exists".format(proc))
            else:
                print("[MTX].[{}] does not exist".format(proc))
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print("FAILED: Schema/Procedure check error - {}".format(e))
        return
    
    # Test 3: Test FetchAllCustomer
    print("\n[TEST 3] Testing FetchAllCustomer")
    print("-" * 40)
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        cursor.execute("EXEC [MTX].[FetchAllCustomer]")
        
        if cursor.description:
            customers = cursor.fetchall()
            print("FetchAllCustomer returned {} customers".format(len(customers)))
            
            if customers:
                print("Sample customer IDs:")
                for i, customer in enumerate(customers[:3]):
                    customer_id = customer[0] if customer else "NULL"
                    print("  {}. {} (type: {})".format(i+1, customer_id, type(customer_id)))
                    
                # Get a sample customer ID for next test
                sample_customer_id = customers[0][0] if customers[0] else None
            else:
                print("No customers returned")
                sample_customer_id = None
        else:
            print("No result set returned")
            sample_customer_id = None
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print("FAILED: FetchAllCustomer error - {}".format(e))
        sample_customer_id = None
    
    # Test 4: Test GetDemographicCallData
    print("\n[TEST 4] Testing GetDemographicCallData")
    print("-" * 40)
    
    # Test with predefined customer IDs and the sample one
    test_customer_ids = ["768", 768]
    if sample_customer_id:
        test_customer_ids.extend([sample_customer_id, str(sample_customer_id)])
        if str(sample_customer_id).isdigit():
            test_customer_ids.append(int(sample_customer_id))
    
    try:
        connection = pyodbc.connect(connection_string_matrix)
        cursor = connection.cursor()
        
        successful_tests = []
        
        for test_id in test_customer_ids:
            try:
                print("  Testing customer ID: {} (type: {})".format(test_id, type(test_id)))
                
                cursor.execute("EXEC [MTX].[GetDemographicCallData] @CustomerID = ?", (test_id,))
                
                if cursor.description:
                    rows = cursor.fetchall()
                    if rows:
                        print("    SUCCESS: Got {} rows".format(len(rows)))
                        print("    Column count: {}".format(len(cursor.description)))
                        print("    First row preview: {}...".format(str(rows[0])[:100]))
                        successful_tests.append((test_id, len(rows)))
                    else:
                        print("    Result set returned but no data rows")
                else:
                    print("    No result set returned")
                    
            except Exception as e:
                print("    Error: {}".format(e))
        
        if successful_tests:
            print("\nSUCCESSFUL TESTS:")
            for test_id, row_count in successful_tests:
                print("  - Customer ID {} ({}): {} rows".format(test_id, type(test_id), row_count))
        else:
            print("\nNO SUCCESSFUL TESTS - All customer IDs failed")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print("FAILED: GetDemographicCallData test error - {}".format(e))
    
    print("\n" + "=" * 80)
    print("TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_stored_procedure_standalone() 