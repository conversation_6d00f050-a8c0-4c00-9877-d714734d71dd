#!/usr/bin/env python3
"""
Test script for NBT (Next Best Time) scoring functionality
"""

def calculate_call_score_and_recommendation(answered_count_15s, call_count, answered_count, total_talk_time, min_call_attempts=3):
    """
    Calculate call score and recommendation based on BestTimeToCallParams_Optimized.sql logic
    
    Args:
        answered_count_15s: Number of calls answered within 15 seconds
        call_count: Total number of calls
        answered_count: Total answered calls
        total_talk_time: Total talk time in seconds
        min_call_attempts: Minimum calls required for scoring (default: 3)
    
    Returns:
        tuple: (call_score, recommendation)
    """
    try:
        # Skip if insufficient data
        if call_count < min_call_attempts:
            return 0, "Not Recommended"
        
        # Calculate answer rate (using 15s+ answered calls)
        answer_rate = answered_count_15s / call_count if call_count > 0 else 0
        
        # Calculate average talk time
        avg_talk_time = total_talk_time / answered_count if answered_count > 0 else 0
        
        # Calculate composite score based on SQL logic
        call_score = (
            (answer_rate * 50) +                                    # Answer rate weight (50%)
            (answered_count_15s * 2) +                             # Volume bonus
            (10 if call_count >= min_call_attempts else 0) +       # Sample size bonus
            (10 if avg_talk_time > 60 else 0)                      # Quality bonus (>1 min talk time)
        )
        
        # Determine recommendation based on score thresholds
        if call_score >= 50:
            recommendation = "Highly Recommended"
        elif call_score >= 30:
            recommendation = "Recommended"
        elif call_score >= 15:
            recommendation = "Consider"
        else:
            recommendation = "Not Recommended"
        
        return call_score, recommendation
        
    except Exception as e:
        print(f"Error calculating call score: {str(e)}")
        return 0, "Not Recommended"

def generate_nbt_data(customer_stats_data):
    """
    Generate NBT (Next Best Time) data from customer statistics
    
    Args:
        customer_stats_data: List of customer stat records (dict format for testing)
    
    Returns:
        dict: NBT data organized by day of week with top 2 time slots per day
    """
    try:
        if not customer_stats_data:
            return {}
        
        # Group data by day and calculate scores
        day_scores = {}
        
        for row in customer_stats_data:
            day_name = row["day_name"]
            
            # Calculate score and recommendation
            call_score, recommendation = calculate_call_score_and_recommendation(
                answered_count_15s=row["answeredCount15s"],
                call_count=row["call_count"],
                answered_count=row["answeredCount"],
                total_talk_time=row["total_talk_time"]
            )
            
            # Skip if score is too low or insufficient data
            if call_score < 10:  # Minimum threshold
                continue
            
            if day_name not in day_scores:
                day_scores[day_name] = []
            
            day_scores[day_name].append({
                "HourOfDay": row["hour_of_day"],
                "CallScore": call_score,
                "Recommendation": recommendation,
                "AnswerRate": (row["answeredCount15s"] / row["call_count"] * 100) if row["call_count"] > 0 else 0,
                "TotalCalls": row["call_count"],
                "AnsweredCalls15s": row["answeredCount15s"]
            })
        
        # Sort each day's time slots by call score (descending) and take top 2
        nbt_data = {}
        for day_name, time_slots in day_scores.items():
            # Sort by call score descending
            sorted_slots = sorted(time_slots, key=lambda x: x["CallScore"], reverse=True)
            
            # Take top 2 slots per day
            top_slots = sorted_slots[:2]
            
            # Format for output (remove extra fields used for sorting)
            formatted_slots = []
            for slot in top_slots:
                formatted_slots.append({
                    "HourOfDay": slot["HourOfDay"],
                    "CallScore": slot["CallScore"],
                    "Recommendation": slot["Recommendation"]
                })
            
            if formatted_slots:  # Only add days that have valid time slots
                nbt_data[day_name] = formatted_slots
        
        return nbt_data
        
    except Exception as e:
        print(f"Error generating NBT data: {str(e)}")
        return {}

def test_nbt_scoring():
    """Test the NBT scoring with sample data"""
    
    # Sample customer statistics data (simulating SQL results)
    sample_data = [
        # Monday data
        {"day_name": "Monday", "hour_of_day": 17, "answeredCount15s": 2, "call_count": 6, "answeredCount": 3, "total_talk_time": 560},
        {"day_name": "Monday", "hour_of_day": 18, "call_count": 5, "answeredCount15s": 1, "answeredCount": 2, "total_talk_time": 120},
        
        # Tuesday data
        {"day_name": "Tuesday", "hour_of_day": 16, "answeredCount15s": 3, "call_count": 8, "answeredCount": 4, "total_talk_time": 720},
        {"day_name": "Tuesday", "hour_of_day": 17, "answeredCount15s": 2, "call_count": 7, "answeredCount": 3, "total_talk_time": 450},
        
        # Wednesday data
        {"day_name": "Wednesday", "hour_of_day": 14, "answeredCount15s": 2, "call_count": 9, "answeredCount": 4, "total_talk_time": 630},
        {"day_name": "Wednesday", "hour_of_day": 15, "answeredCount15s": 1, "call_count": 5, "answeredCount": 2, "total_talk_time": 180},
        
        # Thursday data
        {"day_name": "Thursday", "hour_of_day": 19, "answeredCount15s": 4, "call_count": 6, "answeredCount": 5, "total_talk_time": 800},
        {"day_name": "Thursday", "hour_of_day": 12, "answeredCount15s": 2, "call_count": 9, "answeredCount": 4, "total_talk_time": 540},
        
        # Friday data
        {"day_name": "Friday", "hour_of_day": 14, "answeredCount15s": 3, "call_count": 8, "answeredCount": 4, "total_talk_time": 650},
        {"day_name": "Friday", "hour_of_day": 17, "answeredCount15s": 2, "call_count": 9, "answeredCount": 4, "total_talk_time": 630},
        
        # Saturday data
        {"day_name": "Saturday", "hour_of_day": 10, "answeredCount15s": 2, "call_count": 7, "answeredCount": 3, "total_talk_time": 420},
        {"day_name": "Saturday", "hour_of_day": 15, "answeredCount15s": 3, "call_count": 11, "answeredCount": 5, "total_talk_time": 750},
        
        # Sunday data
        {"day_name": "Sunday", "hour_of_day": 15, "answeredCount15s": 3, "call_count": 7, "answeredCount": 4, "total_talk_time": 600},
        {"day_name": "Sunday", "hour_of_day": 12, "answeredCount15s": 2, "call_count": 9, "answeredCount": 4, "total_talk_time": 630},
    ]
    
    print("🧪 Testing NBT Scoring Algorithm")
    print("=" * 50)
    
    # Test individual scoring
    print("\n📊 Individual Score Calculations:")
    for data in sample_data[:5]:  # Show first 5 examples
        score, recommendation = calculate_call_score_and_recommendation(
            data["answeredCount15s"],
            data["call_count"],
            data["answeredCount"],
            data["total_talk_time"]
        )
        answer_rate = (data["answeredCount15s"] / data["call_count"] * 100) if data["call_count"] > 0 else 0
        avg_talk_time = data["total_talk_time"] / data["answeredCount"] if data["answeredCount"] > 0 else 0
        
        print(f"{data['day_name']} {data['hour_of_day']}:00")
        print(f"  - Answer Rate (15s): {answer_rate:.1f}%")
        print(f"  - Avg Talk Time: {avg_talk_time:.1f}s")
        print(f"  - Call Score: {score:.2f}")
        print(f"  - Recommendation: {recommendation}")
        print()
    
    # Generate NBT data
    nbt_result = generate_nbt_data(sample_data)
    
    print("\n🎯 Generated NBT Data:")
    print("=" * 30)
    
    import json
    print(json.dumps(nbt_result, indent=2))
    
    print("\n✅ NBT Scoring Test Complete!")
    print(f"Generated recommendations for {len(nbt_result)} days")
    
    # Validate the structure
    for day, slots in nbt_result.items():
        if len(slots) > 2:
            print(f"⚠️  Warning: {day} has {len(slots)} slots (expected max 2)")
        for slot in slots:
            required_fields = ["HourOfDay", "CallScore", "Recommendation"]
            for field in required_fields:
                if field not in slot:
                    print(f"❌ Missing field {field} in {day} slot")

if __name__ == "__main__":
    test_nbt_scoring()
